{% extends "base.html" %}

{% set page_title = "简单测试页面" %}

{% block title %}{{ page_title }} - APS智能调度平台{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1>简单测试页面</h1>
            
            <div class="card mt-4">
                <div class="card-header">
                    <h5>API测试</h5>
                </div>
                <div class="card-body">
                    <button class="btn btn-primary" onclick="testFTAPI()">测试FT订单API</button>
                    <button class="btn btn-success" onclick="testCPAPI()">测试CP订单API</button>
                    <button class="btn btn-info" onclick="testEmailAPI()">测试邮箱配置API</button>
                </div>
            </div>
            
            <div class="card mt-4">
                <div class="card-header">
                    <h5>测试结果</h5>
                </div>
                <div class="card-body">
                    <div id="testResults" style="height: 300px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 5px;">
                        <p>等待测试...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function log(message, type = 'info') {
    const results = document.getElementById('testResults');
    const timestamp = new Date().toLocaleTimeString();
    const color = type === 'success' ? 'green' : type === 'error' ? 'red' : 'blue';
    results.innerHTML += `<div style="color: ${color}; margin-bottom: 5px;">[${timestamp}] ${message}</div>`;
    results.scrollTop = results.scrollHeight;
    console.log(`[${type.toUpperCase()}] ${message}`);
}

async function testFTAPI() {
    log('开始测试FT订单API...');
    try {
        const response = await fetch('/api/v2/orders/ft-summary?page=1&size=5', {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            log(`✅ FT订单API成功: 返回${data.data?.length || 0}条记录`, 'success');
            log(`统计信息: 总计=${data.stats?.total || 0}, 有效=${data.stats?.valid || 0}`, 'info');
        } else {
            log(`❌ FT订单API失败: ${response.status} ${response.statusText}`, 'error');
        }
    } catch (error) {
        log(`❌ FT订单API错误: ${error.message}`, 'error');
    }
}

async function testCPAPI() {
    log('开始测试CP订单API...');
    try {
        const response = await fetch('/api/v2/orders/cp-summary?page=1&size=5', {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            log(`✅ CP订单API成功: 返回${data.data?.length || 0}条记录`, 'success');
            log(`统计信息: 总计=${data.stats?.total || 0}, 有效=${data.stats?.valid || 0}`, 'info');
        } else {
            log(`❌ CP订单API失败: ${response.status} ${response.statusText}`, 'error');
        }
    } catch (error) {
        log(`❌ CP订单API错误: ${error.message}`, 'error');
    }
}

async function testEmailAPI() {
    log('开始测试邮箱配置API...');
    try {
        const response = await fetch('/api/email_configs', {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            log(`✅ 邮箱配置API成功: 状态=${data.status}, 配置数量=${data.data?.length || 0}`, 'success');
        } else {
            log(`❌ 邮箱配置API失败: ${response.status} ${response.statusText}`, 'error');
        }
    } catch (error) {
        log(`❌ 邮箱配置API错误: ${error.message}`, 'error');
    }
}

// 页面加载时的初始化
document.addEventListener('DOMContentLoaded', function() {
    log('✅ 简单测试页面加载完成', 'success');
    log('点击按钮开始测试API...', 'info');
});
</script>
{% endblock %}
