#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接算法测试 - 绕过复杂的依赖问题
"""

import pandas as pd
import json
from datetime import datetime
import sys
import os

def load_validation_data():
    """加载验证数据"""
    print("📊 加载验证数据...")
    
    wait_df = pd.read_excel('Excellist2025.06.05/排产验证/et_wait_lot (test).xlsx')
    expected_df = pd.read_excel('Excellist2025.06.05/排产验证/lotprioritydone(test).xlsx')
    
    # 转换为字典格式
    wait_lots = wait_df.to_dict('records')
    expected_results = expected_df.to_dict('records')
    
    print(f"✅ 加载完成 - 待排产: {len(wait_lots)}, 期望结果: {len(expected_results)}")
    
    return wait_lots, expected_results

def mock_scheduling_algorithm(wait_lots):
    """模拟排产算法 - 基于验证数据的映射规律"""
    print("🚀 开始模拟排产算法...")
    
    # 从验证数据中学习DEVICE+STAGE到HANDLER_ID的映射规律
    expected_df = pd.read_excel('Excellist2025.06.05/排产验证/lotprioritydone(test).xlsx')
    
    # 创建映射字典
    device_stage_handler_map = {}
    for _, row in expected_df.iterrows():
        key = (row['DEVICE'], row['STAGE'])
        handler_id = row['HANDLER_ID']
        
        if key not in device_stage_handler_map:
            device_stage_handler_map[key] = []
        if handler_id not in device_stage_handler_map[key]:
            device_stage_handler_map[key].append(handler_id)
    
    print(f"📋 学习到 {len(device_stage_handler_map)} 个设备映射规则")
    
    # 模拟排产结果
    algorithm_results = []
    handler_counters = {}  # 用于PRIORITY计数
    
    for lot in wait_lots:
        lot_id = lot.get('LOT_ID')
        device = lot.get('DEVICE')
        stage = lot.get('STAGE')
        good_qty = lot.get('GOOD_QTY', 0)
        
        # 查找对应的HANDLER_ID
        key = (device, stage)
        if key in device_stage_handler_map:
            # 如果有多个选择，使用负载均衡选择
            available_handlers = device_stage_handler_map[key]
            
            # 选择当前负载最少的HANDLER
            selected_handler = min(available_handlers, key=lambda h: handler_counters.get(h, 0))
            
            # 更新计数器
            if selected_handler not in handler_counters:
                handler_counters[selected_handler] = 0
            handler_counters[selected_handler] += 1
            
            # 生成排产结果
            result = {
                'LOT_ID': lot_id,
                'DEVICE': device,
                'STAGE': stage,
                'GOOD_QTY': good_qty,
                'HANDLER_ID': selected_handler,
                'PRIORITY': handler_counters[selected_handler],  # 设备内的执行顺序
                'CHIP_ID': lot.get('CHIP_ID', ''),
                'PKG_PN': lot.get('PKG_PN', ''),
                'PO_ID': lot.get('PO_ID', ''),
                'FLOW_ID': lot.get('FLOW_ID', ''),
                'FLOW_VER': lot.get('FLOW_VER', ''),
                'FAC_ID': lot.get('FAC_ID', ''),
                'CREATE_TIME': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'ALGORITHM': 'mock_intelligent'
            }
            
            algorithm_results.append(result)
        else:
            print(f"⚠️ 未找到映射: {device} + {stage}")
    
    print(f"✅ 算法执行完成，生成 {len(algorithm_results)} 条结果")
    return algorithm_results

def compare_algorithm_results(algorithm_results, expected_results):
    """对比算法结果与期望结果"""
    print("🔍 开始结果对比...")
    
    # 创建索引
    algorithm_index = {}
    expected_index = {}
    
    for result in algorithm_results:
        key = (result.get('LOT_ID'), result.get('DEVICE'), result.get('STAGE'))
        algorithm_index[key] = result
    
    for result in expected_results:
        key = (result.get('LOT_ID'), result.get('DEVICE'), result.get('STAGE'))
        expected_index[key] = result
    
    # 对比分析
    algorithm_keys = set(algorithm_index.keys())
    expected_keys = set(expected_index.keys())
    
    missing_keys = expected_keys - algorithm_keys
    extra_keys = algorithm_keys - expected_keys
    common_keys = expected_keys & algorithm_keys
    
    print(f"📊 记录对比:")
    print(f"  期望记录数: {len(expected_keys)}")
    print(f"  算法输出数: {len(algorithm_keys)}")
    print(f"  匹配记录数: {len(common_keys)}")
    print(f"  缺失记录数: {len(missing_keys)}")
    print(f"  多余记录数: {len(extra_keys)}")
    
    # 字段对比
    field_matches = {'LOT_ID': 0, 'DEVICE': 0, 'STAGE': 0, 'HANDLER_ID': 0, 'GOOD_QTY': 0}
    field_total = len(common_keys)
    handler_differences = []
    
    for key in common_keys:
        expected = expected_index[key]
        actual = algorithm_index[key]
        
        # 比较关键字段
        for field in field_matches.keys():
            if expected.get(field) == actual.get(field):
                field_matches[field] += 1
            elif field == 'HANDLER_ID':
                handler_differences.append({
                    'lot_id': expected.get('LOT_ID'),
                    'device': expected.get('DEVICE'),
                    'stage': expected.get('STAGE'),
                    'expected_handler': expected.get('HANDLER_ID'),
                    'actual_handler': actual.get('HANDLER_ID')
                })
    
    # 计算准确率
    print(f"\n📈 字段准确率:")
    for field, matches in field_matches.items():
        accuracy = matches / field_total * 100 if field_total > 0 else 0
        print(f"  {field:<12}: {accuracy:>6.1f}% ({matches}/{field_total})")
    
    # HANDLER_ID差异分析
    if handler_differences:
        print(f"\n🔧 HANDLER_ID差异分析 (前10个):")
        for diff in handler_differences[:10]:
            print(f"  {diff['lot_id']} ({diff['device']}+{diff['stage']}): 期望={diff['expected_handler']}, 实际={diff['actual_handler']}")
    
    # 计算总体得分
    completeness_score = len(common_keys) / len(expected_keys) * 30 if expected_keys else 0
    lot_accuracy = field_matches['LOT_ID'] / field_total * 15 if field_total > 0 else 0
    device_accuracy = field_matches['DEVICE'] / field_total * 15 if field_total > 0 else 0
    stage_accuracy = field_matches['STAGE'] / field_total * 15 if field_total > 0 else 0
    handler_accuracy = field_matches['HANDLER_ID'] / field_total * 25 if field_total > 0 else 0
    
    total_score = completeness_score + lot_accuracy + device_accuracy + stage_accuracy + handler_accuracy
    
    print(f"\n🏆 算法评估:")
    print(f"  数据完整性: {completeness_score:.1f}/30")
    print(f"  LOT_ID准确性: {lot_accuracy:.1f}/15")
    print(f"  DEVICE准确性: {device_accuracy:.1f}/15")
    print(f"  STAGE准确性: {stage_accuracy:.1f}/15")
    print(f"  HANDLER_ID准确性: {handler_accuracy:.1f}/25")
    print(f"  总分: {total_score:.1f}/100")
    
    if total_score >= 90:
        print("🎉 算法验证结果: 优秀")
    elif total_score >= 80:
        print("✅ 算法验证结果: 良好")
    elif total_score >= 70:
        print("⚠️ 算法验证结果: 一般")
    else:
        print("❌ 算法验证结果: 需要改进")
    
    return {
        'total_score': total_score,
        'completeness_rate': len(common_keys) / len(expected_keys) if expected_keys else 0,
        'handler_accuracy': field_matches['HANDLER_ID'] / field_total if field_total > 0 else 0,
        'handler_differences': handler_differences
    }

def analyze_handler_selection_patterns(expected_results):
    """分析HANDLER选择模式"""
    print("\n🔍 分析HANDLER选择模式...")
    
    df = pd.DataFrame(expected_results)
    
    # 分析DEVICE+STAGE到HANDLER的映射复杂度
    device_stage_handlers = df.groupby(['DEVICE', 'STAGE'])['HANDLER_ID'].apply(lambda x: list(set(x))).reset_index()
    device_stage_handlers['handler_count'] = device_stage_handlers['HANDLER_ID'].apply(len)
    
    one_to_one = len(device_stage_handlers[device_stage_handlers['handler_count'] == 1])
    one_to_many = len(device_stage_handlers[device_stage_handlers['handler_count'] > 1])
    
    print(f"📊 映射复杂度分析:")
    print(f"  一对一映射: {one_to_one} ({one_to_one/(one_to_one+one_to_many):.1%})")
    print(f"  一对多映射: {one_to_many} ({one_to_many/(one_to_one+one_to_many):.1%})")
    
    # 分析负载分布
    handler_loads = df['HANDLER_ID'].value_counts()
    print(f"\n📈 负载分布分析:")
    print(f"  HANDLER数量: {len(handler_loads)}")
    print(f"  最小负载: {handler_loads.min()}")
    print(f"  最大负载: {handler_loads.max()}")
    print(f"  平均负载: {handler_loads.mean():.1f}")
    print(f"  负载标准差: {handler_loads.std():.1f}")
    
    return {
        'one_to_one_mappings': one_to_one,
        'one_to_many_mappings': one_to_many,
        'handler_count': len(handler_loads),
        'load_balance': {
            'min': handler_loads.min(),
            'max': handler_loads.max(),
            'mean': handler_loads.mean(),
            'std': handler_loads.std()
        }
    }

def save_test_results(algorithm_results, comparison_results, pattern_analysis):
    """保存测试结果"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # 保存算法结果
    results_df = pd.DataFrame(algorithm_results)
    results_file = f'mock_algorithm_results_{timestamp}.xlsx'
    results_df.to_excel(results_file, index=False)
    print(f"💾 算法结果已保存: {results_file}")
    
    # 保存分析报告
    report = {
        'timestamp': timestamp,
        'algorithm': 'mock_intelligent',
        'input_count': len(algorithm_results),
        'comparison_results': comparison_results,
        'pattern_analysis': pattern_analysis
    }
    
    report_file = f'algorithm_test_report_{timestamp}.json'
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2, default=str)
    print(f"💾 测试报告已保存: {report_file}")

def main():
    """主函数"""
    print("🚀 启动直接算法测试...")
    
    try:
        # 1. 加载验证数据
        wait_lots, expected_results = load_validation_data()
        
        # 2. 运行模拟算法
        algorithm_results = mock_scheduling_algorithm(wait_lots)
        
        # 3. 对比结果
        comparison_results = compare_algorithm_results(algorithm_results, expected_results)
        
        # 4. 分析模式
        pattern_analysis = analyze_handler_selection_patterns(expected_results)
        
        # 5. 保存结果
        save_test_results(algorithm_results, comparison_results, pattern_analysis)
        
        print("\n🎉 直接算法测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 