#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql

try:
    conn = pymysql.connect(
        host='localhost', 
        user='root', 
        password='WWWwww123!', 
        database='aps_system', 
        charset='utf8mb4'
    )
    cursor = conn.cursor()
    
    cursor.execute('SHOW TABLES')
    tables = cursor.fetchall()
    print('aps_system数据库中的表:')
    for table in tables:
        print(f'  {table[0]}')

    # 检查邮箱配置表
    cursor.execute("SHOW TABLES LIKE 'email_configs'")
    email_table = cursor.fetchone()
    print(f'\n邮箱配置表存在: {bool(email_table)}')

    if email_table:
        cursor.execute('SELECT COUNT(*) FROM email_configs')
        email_count = cursor.fetchone()[0]
        print(f'邮箱配置记录数: {email_count}')

    cursor.close()
    conn.close()
    
except Exception as e:
    print(f'数据库连接错误: {e}')
