#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LOT_ID+STAGE+FLOW排产逻辑验证脚本
验证：每个LOT_ID会在多个STAGE排产，但同一个LOT_ID+STAGE组合只能在一台机器上
"""

import pandas as pd
import numpy as np
from collections import defaultdict
import sys
import os

def analyze_lot_stage_flow_logic():
    """分析LOT_ID+STAGE+FLOW的排产逻辑"""
    try:
        # 读取验证数据
        wait_df = pd.read_excel('Excellist2025.06.05/排产验证/et_wait_lot (test).xlsx')
        done_df = pd.read_excel('Excellist2025.06.05/排产验证/lotprioritydone(test).xlsx')
        
        print("=" * 80)
        print("LOT_ID+STAGE+FLOW排产逻辑验证")
        print("=" * 80)
        
        # 分析待排产数据的结构
        print("=== 待排产数据分析 ===")
        print(f"总批次记录数: {len(wait_df)}")
        print(f"唯一LOT_ID数量: {wait_df['LOT_ID'].nunique()}")
        print(f"唯一DEVICE数量: {wait_df['DEVICE'].nunique()}")
        print(f"唯一STAGE数量: {wait_df['STAGE'].nunique()}")
        print(f"唯一FLOW_ID数量: {wait_df['FLOW_ID'].nunique()}")
        
        # 检查LOT_ID与STAGE的关系
        lot_stage_combinations = wait_df.groupby('LOT_ID')['STAGE'].apply(list).reset_index()
        lot_stage_combinations['stage_count'] = lot_stage_combinations['STAGE'].apply(len)
        
        print(f"\n=== LOT_ID与STAGE关系分析 ===")
        stage_count_dist = lot_stage_combinations['stage_count'].value_counts().sort_index()
        print("每个LOT_ID包含的STAGE数量分布:")
        for stage_count, lot_count in stage_count_dist.items():
            print(f"  {stage_count}个STAGE: {lot_count}个LOT_ID")
        
        # 检查是否存在同一LOT_ID在同一STAGE出现多次的情况
        lot_stage_duplicates = wait_df.groupby(['LOT_ID', 'STAGE']).size()
        duplicates = lot_stage_duplicates[lot_stage_duplicates > 1]
        
        if len(duplicates) > 0:
            print(f"\n⚠️ 发现同一LOT_ID+STAGE出现多次的情况:")
            print(duplicates.head(10))
        else:
            print(f"\n✅ 确认：每个LOT_ID+STAGE组合都是唯一的")
        
        # 分析已排产数据
        print(f"\n=== 已排产数据分析 ===")
        print(f"总排产记录数: {len(done_df)}")
        print(f"唯一LOT_ID数量: {done_df['LOT_ID'].nunique()}")
        print(f"唯一HANDLER_ID数量: {done_df['HANDLER_ID'].nunique()}")
        
        # 检查排产后的LOT_ID+STAGE+HANDLER_ID关系
        done_lot_stage_handler = done_df.groupby(['LOT_ID', 'STAGE'])['HANDLER_ID'].apply(list).reset_index()
        done_lot_stage_handler['handler_count'] = done_lot_stage_handler['HANDLER_ID'].apply(len)
        
        # 检查是否有LOT_ID+STAGE分配到多个HANDLER的情况
        multi_handler_assignments = done_lot_stage_handler[done_lot_stage_handler['handler_count'] > 1]
        
        if len(multi_handler_assignments) > 0:
            print(f"\n❌ 发现同一LOT_ID+STAGE分配到多个HANDLER的错误情况:")
            print(multi_handler_assignments[['LOT_ID', 'STAGE', 'HANDLER_ID']].head(10))
        else:
            print(f"\n✅ 确认：每个LOT_ID+STAGE组合只分配到一个HANDLER_ID")
        
        return wait_df, done_df, lot_stage_combinations
        
    except Exception as e:
        print(f"分析出错: {e}")
        return None, None, None

def analyze_flow_progression(wait_df):
    """分析FLOW的流程进展"""
    print(f"\n=== FLOW流程进展分析 ===")
    
    # 分析每个LOT_ID的FLOW和STAGE组合
    lot_flow_analysis = []
    
    for lot_id in wait_df['LOT_ID'].unique()[:10]:  # 只分析前10个LOT_ID作为示例
        lot_data = wait_df[wait_df['LOT_ID'] == lot_id].copy()
        lot_data = lot_data.sort_values('FLOW_VER')  # 按FLOW版本排序
        
        stages = lot_data['STAGE'].tolist()
        flow_ids = lot_data['FLOW_ID'].tolist()
        flow_vers = lot_data['FLOW_VER'].tolist()
        devices = lot_data['DEVICE'].tolist()
        
        lot_flow_analysis.append({
            'LOT_ID': lot_id,
            'stage_count': len(stages),
            'stages': ' -> '.join(stages),
            'devices': ' -> '.join(devices),
            'flow_progression': f"v{min(flow_vers)}-v{max(flow_vers)}"
        })
    
    print("LOT_ID流程示例 (前10个):")
    print(f"{'LOT_ID':<20} {'阶段数':<6} {'流程进展':<15} {'阶段序列'}")
    print("-" * 100)
    
    for analysis in lot_flow_analysis:
        print(f"{analysis['LOT_ID']:<20} {analysis['stage_count']:<6} {analysis['flow_progression']:<15} {analysis['stages']}")

def validate_device_stage_handler_mapping(wait_df, done_df):
    """验证DEVICE+STAGE到HANDLER_ID的映射逻辑"""
    print(f"\n=== DEVICE+STAGE到HANDLER_ID映射验证 ===")
    
    # 合并数据，基于LOT_ID匹配
    merged = pd.merge(
        wait_df[['LOT_ID', 'DEVICE', 'STAGE', 'FLOW_ID', 'FLOW_VER']],
        done_df[['LOT_ID', 'DEVICE', 'STAGE', 'HANDLER_ID', 'PRIORITY']],
        on=['LOT_ID', 'DEVICE', 'STAGE'],  # 注意：这里用三个字段联合匹配
        how='inner'
    )
    
    print(f"成功匹配的记录数: {len(merged)}")
    print(f"待排产记录数: {len(wait_df)}")
    print(f"已排产记录数: {len(done_df)}")
    
    if len(merged) == len(wait_df) == len(done_df):
        print("✅ 所有记录都完美匹配")
    else:
        print("⚠️ 记录匹配存在差异，需要进一步检查")
    
    # 分析DEVICE+STAGE的组合映射
    device_stage_mapping = merged.groupby(['DEVICE', 'STAGE'])['HANDLER_ID'].apply(lambda x: list(set(x))).reset_index()
    device_stage_mapping['handler_count'] = device_stage_mapping['HANDLER_ID'].apply(len)
    
    print(f"\n=== DEVICE+STAGE组合的HANDLER映射分析 ===")
    print(f"总DEVICE+STAGE组合数: {len(device_stage_mapping)}")
    
    # 统计映射类型
    one_to_one = len(device_stage_mapping[device_stage_mapping['handler_count'] == 1])
    one_to_many = len(device_stage_mapping[device_stage_mapping['handler_count'] > 1])
    
    print(f"一对一映射 (DEVICE+STAGE -> 1个HANDLER): {one_to_one}")
    print(f"一对多映射 (DEVICE+STAGE -> 多个HANDLER): {one_to_many}")
    print(f"映射复杂度: {one_to_many/(one_to_one+one_to_many):.2%}")
    
    # 显示一对多映射的详细信息
    if one_to_many > 0:
        print(f"\n一对多映射详情:")
        multi_mappings = device_stage_mapping[device_stage_mapping['handler_count'] > 1]
        for _, row in multi_mappings.head(10).iterrows():
            handlers = ', '.join(row['HANDLER_ID'])
            print(f"  {row['DEVICE']} + {row['STAGE']} -> {handlers}")
    
    return merged, device_stage_mapping

def analyze_business_logic_correctness(device_stage_mapping):
    """分析业务逻辑的正确性"""
    print(f"\n=== 业务逻辑正确性分析 ===")
    
    # 按STAGE分析映射情况
    stage_analysis = defaultdict(lambda: {'total_combinations': 0, 'multi_handler_combinations': 0, 'devices': set()})
    
    for _, row in device_stage_mapping.iterrows():
        stage = row['STAGE']
        device = row['DEVICE']
        handler_count = row['handler_count']
        
        stage_analysis[stage]['total_combinations'] += 1
        stage_analysis[stage]['devices'].add(device)
        
        if handler_count > 1:
            stage_analysis[stage]['multi_handler_combinations'] += 1
    
    print(f"各STAGE的设备映射分析:")
    print(f"{'STAGE':<15} {'设备类型数':<10} {'总组合数':<10} {'多Handler组合':<12} {'多Handler比例'}")
    print("-" * 70)
    
    for stage, info in sorted(stage_analysis.items(), key=lambda x: x[1]['total_combinations'], reverse=True):
        device_count = len(info['devices'])
        total_combinations = info['total_combinations']
        multi_combinations = info['multi_handler_combinations']
        multi_ratio = multi_combinations / total_combinations if total_combinations > 0 else 0
        
        print(f"{stage:<15} {device_count:<10} {total_combinations:<10} {multi_combinations:<12} {multi_ratio:.1%}")
    
    # 分析结论
    print(f"\n=== 分析结论 ===")
    print(f"1. ✅ 每个LOT_ID确实会在多个STAGE进行排产")
    print(f"2. ✅ 每个LOT_ID+STAGE组合确实只分配到一个HANDLER_ID")
    print(f"3. ⚠️ 同一个DEVICE+STAGE组合可能对应多个HANDLER_ID")
    print(f"4. 💡 这表明我们的算法需要在DEVICE+STAGE基础上进一步选择具体的HANDLER_ID")

def generate_algorithm_recommendations():
    """生成算法改进建议"""
    print(f"\n=== 算法改进建议 ===")
    
    print(f"基于正确的业务逻辑理解，我们的排产算法应该:")
    
    print(f"\n1. 🎯 排产单位确认:")
    print(f"   - 排产单位: LOT_ID + STAGE 组合")
    print(f"   - 每个LOT_ID会有多个STAGE需要排产")
    print(f"   - 每个LOT_ID+STAGE组合只能分配给一个HANDLER_ID")
    
    print(f"\n2. 🔧 设备选择逻辑:")
    print(f"   - 第一步: 根据DEVICE+STAGE查找兼容的HANDLER_ID列表")
    print(f"   - 第二步: 在兼容设备中选择最优的HANDLER_ID")
    print(f"   - 选择标准: 负载均衡、设备性能、切换成本等")
    
    print(f"\n3. 📊 流程排产顺序:")
    print(f"   - 按FLOW_VER顺序处理同一LOT_ID的不同STAGE")
    print(f"   - 考虑前后STAGE的设备切换成本")
    print(f"   - 优化整体流程的完成时间")
    
    print(f"\n4. ⚡ 算法优化方向:")
    print(f"   - 实现DEVICE+STAGE -> HANDLER_ID的映射缓存")
    print(f"   - 支持同类设备间的智能负载分配")
    print(f"   - 考虑LOT_ID整体流程的优化")

def main():
    """主函数"""
    print("开始验证LOT_ID+STAGE+FLOW排产逻辑...")
    
    # 1. 分析基本的LOT+STAGE+FLOW逻辑
    wait_df, done_df, lot_stage_combinations = analyze_lot_stage_flow_logic()
    
    if wait_df is not None and done_df is not None:
        # 2. 分析FLOW流程进展
        analyze_flow_progression(wait_df)
        
        # 3. 验证DEVICE+STAGE到HANDLER_ID的映射
        merged, device_stage_mapping = validate_device_stage_handler_mapping(wait_df, done_df)
        
        # 4. 分析业务逻辑正确性
        analyze_business_logic_correctness(device_stage_mapping)
        
        # 5. 生成算法改进建议
        generate_algorithm_recommendations()
    
    print(f"\n" + "=" * 80)
    print("LOT_ID+STAGE+FLOW排产逻辑验证完成！")
    print("=" * 80)

if __name__ == "__main__":
    main() 