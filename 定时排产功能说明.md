# 定时排产功能说明

## 功能概述

在Excel数据导入管理中心的左侧区域（col-md-5）新增了定时排产功能，允许用户设置自动化的排产任务，实现无人值守的定时排产和自动保存。

## 功能特点

### 🎯 核心功能
- **多种定时模式**：支持一次性、每日、每周、间隔重复任务
- **自动排产**：定时执行手动排产算法
- **自动保存**：排产完成后自动保存到数据库和历史记录
- **任务管理**：支持暂停、恢复、删除任务
- **实时状态**：显示下次执行时间和任务状态

### 🛠️ 技术实现
- **前端定时器**：使用JavaScript setTimeout实现精确定时
- **本地存储**：任务配置保存在localStorage
- **状态管理**：实时监控和更新任务状态
- **错误处理**：完善的异常处理和状态恢复

## 界面设计

### 定时排产控制区域
位置：Excel文件上传区域下方
```html
<!-- 定时排产功能 -->
<div class="scheduled-task-section">
    <h6>⏰ 定时排产</h6>
    <button>📅 设置定时任务</button>
    <button>📋 查看任务</button>
    <div>当前状态显示</div>
</div>
```

### 任务设置模态框
- **任务基本信息**：名称、类型
- **执行时间设置**：日期、时间、重复规则
- **排产参数**：策略、优化目标
- **高级选项**：自动导入、邮件通知
- **任务预览**：实时显示配置效果

### 任务管理模态框
- **任务列表**：显示所有定时任务
- **状态监控**：活跃、暂停、完成、错误状态
- **操作控制**：暂停、恢复、删除任务

## 任务类型详解

### 1. 一次性任务
- **用途**：在指定的日期和时间执行一次排产
- **配置**：选择具体日期和时间
- **示例**：2024-12-30 09:00 执行排产

### 2. 每日重复
- **用途**：每天在固定时间执行排产
- **配置**：设置执行时间（小时:分钟）
- **示例**：每天 09:00 执行排产

### 3. 每周重复
- **用途**：每周的指定星期执行排产
- **配置**：选择星期几和执行时间
- **示例**：每周一、三、五 09:00 执行排产

### 4. 间隔重复
- **用途**：按固定间隔重复执行排产
- **配置**：设置间隔时间（分钟/小时/天）
- **示例**：每 2 小时执行一次排产

## 使用流程

### 创建定时任务
1. 点击"设置定时任务"按钮
2. 填写任务名称和类型
3. 配置执行时间和重复规则
4. 选择排产策略和优化目标
5. 设置高级选项（可选）
6. 预览任务配置
7. 保存任务

### 管理定时任务
1. 点击"查看任务"按钮
2. 查看所有任务的状态和下次执行时间
3. 可以暂停、恢复或删除任务
4. 监控任务执行结果

### 任务执行流程
```
定时器触发 → 检查是否需要自动导入数据 → 设置排产参数 → 执行排产算法 → 自动保存结果 → 更新任务状态 → 设置下次执行时间（重复任务）
```

## 状态说明

### 任务状态
- **🟢 活跃**：任务正常运行，等待下次执行
- **🟡 暂停**：任务已暂停，不会执行
- **🔵 已完成**：一次性任务已完成
- **🔴 错误**：任务执行过程中出现错误
- **⚫ 已过期**：一次性任务的执行时间已过

### 状态显示
- **当前无定时任务**：没有活跃的定时任务
- **下次执行**：显示最近的任务名称和执行时间
- **任务执行中**：正在执行排产任务

## 高级功能

### 自动导入数据
- **功能**：执行排产前自动导入最新的Excel数据
- **用途**：确保排产使用最新的生产数据
- **状态**：功能接口预留，待后续实现

### 邮件通知
- **功能**：排产完成后发送邮件通知
- **用途**：及时通知管理人员排产结果
- **状态**：功能接口预留，待后续实现

## 数据存储

### 本地存储（localStorage）
```javascript
{
    "scheduledTasks": [
        {
            "id": 1703123456789,
            "name": "每日自动排产",
            "type": "daily",
            "hour": 9,
            "minute": 0,
            "strategy": "intelligent",
            "target": "balanced",
            "status": "active",
            "nextExecution": "2024-12-30T09:00:00.000Z",
            "lastExecuted": "2024-12-29T09:00:00.000Z"
        }
    ]
}
```

### 任务恢复机制
- **页面刷新**：自动恢复所有活跃任务的定时器
- **浏览器重启**：重新计算执行时间并设置定时器
- **过期处理**：自动标记过期的一次性任务

## 性能优化

### 定时器管理
- **精确定时**：使用setTimeout实现毫秒级精度
- **内存管理**：及时清理无效的定时器
- **状态同步**：每分钟更新一次状态显示

### 错误处理
- **任务执行失败**：标记错误状态，不影响其他任务
- **网络异常**：记录错误日志，等待下次执行
- **数据异常**：提供详细的错误信息

## 安全考虑

### 输入验证
- **时间范围**：不允许设置过去的时间
- **参数检查**：验证所有必填字段
- **类型检查**：确保数据类型正确

### 权限控制
- **前端限制**：基于localStorage的本地权限
- **操作确认**：删除任务需要确认
- **状态保护**：防止意外修改任务状态

## 使用建议

### 最佳实践
1. **合理设置频率**：避免过于频繁的执行
2. **监控任务状态**：定期检查任务执行情况
3. **备份重要任务**：记录关键任务的配置
4. **测试后使用**：先测试手动排产再设置定时任务

### 注意事项
1. **浏览器要求**：需要保持浏览器标签页打开
2. **网络连接**：确保网络连接稳定
3. **数据准备**：确保有足够的数据进行排产
4. **系统资源**：避免设置过多的并发任务

## 故障排除

### 常见问题
1. **任务不执行**：检查任务状态和下次执行时间
2. **时间不准确**：检查系统时间设置
3. **数据丢失**：检查localStorage是否被清理
4. **执行失败**：查看控制台错误日志

### 解决方案
1. **重新创建任务**：删除问题任务并重新设置
2. **刷新页面**：重新加载任务配置
3. **检查网络**：确保API接口正常
4. **联系支持**：提供详细的错误信息

---

*功能版本：v1.0*  
*最后更新：2024年12月*  
*技术栈：HTML5 + CSS3 + JavaScript + Bootstrap 5* 