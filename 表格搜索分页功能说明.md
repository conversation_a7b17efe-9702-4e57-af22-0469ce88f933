# 🔍 排产结果表格搜索分页功能说明

## 📋 功能概述

为排产结果表格添加了强大的搜索和分页功能，让用户能够高效地查看和管理大量排产数据。

## ✨ 主要特性

### 1. 智能搜索功能
- **全字段搜索**：可以在所有字段中搜索关键词
- **指定字段搜索**：可以选择特定字段进行精确搜索
- **实时搜索**：输入关键词后自动搜索，无需点击按钮
- **防抖处理**：避免频繁搜索，提升性能

### 2. 分页显示功能
- **灵活分页**：支持每页10/20/50/100条记录
- **智能分页控件**：显示页码、上一页、下一页
- **分页信息**：显示当前页、总页数、总记录数
- **快速跳转**：可以直接点击页码跳转

### 3. 数据统计功能
- **实时统计**：显示总记录数和筛选后的记录数
- **状态提示**：清晰显示当前查看的数据范围

## 🎯 使用方法

### 搜索功能使用
1. **全字段搜索**：
   - 在搜索框中输入关键词
   - 系统会在所有字段中查找匹配的记录
   - 例如：输入"HANK"会找到所有包含"HANK"的记录

2. **指定字段搜索**：
   - 在下拉菜单中选择要搜索的字段
   - 在搜索框中输入关键词
   - 例如：选择"分选机"字段，输入"C-002"

3. **清除搜索**：
   - 点击"清除"按钮重置搜索条件
   - 或者直接清空搜索框内容

### 分页功能使用
1. **设置每页显示数量**：
   - 在"每页显示"下拉菜单中选择数量
   - 支持10、20、50、100条记录

2. **页面导航**：
   - 点击页码直接跳转到指定页面
   - 使用"上一页"/"下一页"按钮逐页浏览
   - 页码过多时会显示省略号

3. **查看分页信息**：
   - 底部显示"显示第X-Y条，共Z条记录"
   - 右上角显示"共X条，显示Y条"

## 🛠️ 技术特性

### 性能优化
- **防抖搜索**：300ms延迟，避免频繁查询
- **内存计算**：所有搜索和分页都在前端完成
- **智能渲染**：只渲染当前页的数据，提升性能

### 用户体验
- **响应式设计**：在移动设备上自动适配
- **视觉反馈**：搜索时的加载状态和动画效果
- **键盘友好**：支持Tab键导航

### 数据导出
- **筛选导出**：可以导出当前搜索结果
- **格式完整**：保持原有的Excel导出格式
- **文件命名**：自动生成带时间戳的文件名

## 📊 搜索字段说明

| 字段名 | 说明 | 示例 |
|--------|------|------|
| 全部字段 | 在所有字段中搜索 | "HANK"、"HOT-FT" |
| 分选机 | 搜索分选机编号 | "HANK-C-002-5800" |
| 工单号 | 搜索内部工单号 | "YX240000332" |
| 产品名称 | 搜索产品名称 | "JW85022-SDA1" |
| 工序阶段 | 搜索工序阶段 | "HOT-FT"、"COLD-FT" |
| 封装类型 | 搜索封装类型 | "QFN3.5*3.5-18" |
| 订单号 | 搜索订单号 | "JP20203F_JC3" |

## 🎨 界面说明

### 搜索栏布局
```
[🔍 搜索框] [字段选择] [每页数量] [清除] [导出筛选] [记录统计]
```

### 分页栏布局
```
显示第1-20条，共169条记录    [« 1 2 3 4 5 »]
```

## 💡 使用技巧

1. **快速查找**：使用全字段搜索快速定位相关记录
2. **精确筛选**：使用指定字段搜索进行精确筛选
3. **批量查看**：调整每页显示数量来适应不同的查看需求
4. **导出筛选**：搜索后可以直接导出筛选结果
5. **状态保持**：搜索和分页状态会在操作过程中保持

## 🔧 故障排除

### 常见问题
1. **搜索无结果**：
   - 检查关键词是否正确
   - 尝试使用部分关键词
   - 切换到"全部字段"搜索

2. **分页异常**：
   - 刷新页面重新加载数据
   - 检查数据是否正常加载

3. **导出失败**：
   - 确保有搜索结果
   - 检查浏览器是否允许下载
   - 尝试减少导出的数据量

### 性能建议
- 大数据量时建议使用分页查看
- 搜索时使用具体的关键词提高效率
- 定期清除搜索条件避免界面混乱

## 📈 更新日志

### v1.0 (2025-01-28)
- ✅ 添加实时搜索功能
- ✅ 实现分页显示
- ✅ 优化用户界面
- ✅ 添加数据统计
- ✅ 支持筛选导出
- ✅ 响应式设计适配

---

> 💡 **提示**：此功能完全在前端实现，无需额外的服务器配置，性能优秀且响应快速。 