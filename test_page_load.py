#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试页面加载和JavaScript功能
"""

import requests
import json
from bs4 import BeautifulSoup

# API基础URL
BASE_URL = "http://127.0.0.1:5000"
LOGIN_URL = f"{BASE_URL}/auth/login"
ALGORITHM_PAGE_URL = f"{BASE_URL}/production/algorithm"

def login_and_get_session():
    """登录并获取会话"""
    session = requests.Session()
    
    # 首先获取登录页面
    try:
        login_page = session.get(f"{BASE_URL}/auth/login")
        print(f"登录页面状态: {login_page.status_code}")
    except Exception as e:
        print(f"无法访问登录页面: {e}")
        return None
    
    # 登录
    login_data = {
        'username': 'admin',
        'password': 'admin'
    }
    
    try:
        response = session.post(LOGIN_URL, data=login_data, allow_redirects=False)
        print(f"登录响应状态: {response.status_code}")
        
        if response.status_code in [200, 302]:
            print("✅ 登录成功")
            return session
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"登录请求失败: {e}")
        return None

def test_algorithm_page(session):
    """测试算法页面"""
    print(f"\n🔍 测试算法页面: {ALGORITHM_PAGE_URL}")
    
    try:
        response = session.get(ALGORITHM_PAGE_URL)
        print(f"页面响应状态: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 页面加载成功")
            
            # 解析HTML内容
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 检查关键元素
            strategy_selector = soup.find('select', {'id': 'strategySelector'})
            if strategy_selector:
                print("✅ 找到策略选择器")
                options = strategy_selector.find_all('option')
                print(f"策略选项数量: {len(options)}")
                for option in options:
                    print(f"  - {option.get('value')}: {option.text.strip()}")
            else:
                print("❌ 未找到策略选择器")
            
            # 检查权重输入框
            weight_fields = [
                'tech_match_weight', 'load_balance_weight', 'deadline_weight',
                'value_efficiency_weight', 'business_priority_weight'
            ]
            
            missing_fields = []
            for field in weight_fields:
                element = soup.find('input', {'id': field})
                if element:
                    print(f"✅ 找到权重字段: {field}")
                else:
                    print(f"❌ 未找到权重字段: {field}")
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"❌ 缺失的权重字段: {missing_fields}")
            else:
                print("✅ 所有权重字段都存在")
            
            # 检查JavaScript代码
            scripts = soup.find_all('script')
            js_found = False
            for script in scripts:
                if script.string and 'loadWeights' in script.string:
                    js_found = True
                    print("✅ 找到loadWeights函数")
                    break
            
            if not js_found:
                print("❌ 未找到loadWeights函数")
            
            # 检查jQuery
            jquery_found = False
            for script in scripts:
                if script.get('src') and 'jquery' in script.get('src', '').lower():
                    jquery_found = True
                    print("✅ 找到jQuery引用")
                    break
            
            if not jquery_found:
                print("⚠️ 未找到jQuery引用，可能影响JavaScript功能")
            
            return True
            
        else:
            print(f"❌ 页面加载失败: {response.status_code}")
            print(f"响应内容: {response.text[:500]}")
            return False
            
    except Exception as e:
        print(f"❌ 页面测试异常: {e}")
        return False

def test_api_directly(session):
    """直接测试API"""
    print(f"\n🔍 直接测试API...")
    
    strategies = ['intelligent', 'deadline', 'product', 'value']
    
    for strategy in strategies:
        try:
            url = f"{BASE_URL}/api/production/algorithm-weights?strategy={strategy}"
            response = session.get(url)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    weights = data.get('weights', {})
                    print(f"✅ {strategy}: 技术匹配度={weights.get('tech_match_weight', 0)}%")
                else:
                    print(f"❌ {strategy}: API返回失败 - {data.get('message', '未知错误')}")
            else:
                print(f"❌ {strategy}: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ {strategy}: 异常 - {e}")

def main():
    """主函数"""
    print("🔧 开始测试算法权重配置页面...")
    
    # 登录
    session = login_and_get_session()
    if not session:
        print("❌ 无法登录，测试终止")
        return
    
    # 测试页面
    page_ok = test_algorithm_page(session)
    
    # 测试API
    test_api_directly(session)
    
    if page_ok:
        print("\n✅ 页面基本结构正常，请检查浏览器控制台是否有JavaScript错误")
        print("💡 建议操作:")
        print("  1. 打开浏览器开发者工具 (F12)")
        print("  2. 查看Console标签页是否有错误信息")
        print("  3. 查看Network标签页检查API请求是否正常")
        print("  4. 手动在页面上切换策略选择器测试")
    else:
        print("\n❌ 页面结构存在问题，需要进一步调试")

if __name__ == "__main__":
    main()
