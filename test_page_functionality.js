// 在浏览器控制台中运行此脚本来测试页面功能

console.log('🔍 开始测试页面功能...');

// 1. 检查全局变量
console.log('1. 检查全局变量:');
console.log('   currentOrderType:', typeof currentOrderType !== 'undefined' ? currentOrderType : '未定义');
console.log('   orderData:', typeof orderData !== 'undefined' ? orderData : '未定义');
console.log('   API_ENDPOINTS:', typeof API_ENDPOINTS !== 'undefined' ? API_ENDPOINTS : '未定义');

// 2. 检查DOM元素
console.log('2. 检查DOM元素:');
const elements = [
    'orderTableBody',
    'totalOrderRecords', 
    'validOrderRecords',
    'engineeringOrderCount',
    'productionOrderCount',
    'orderLoadingOverlay'
];

elements.forEach(id => {
    const element = document.getElementById(id);
    console.log(`   ${id}:`, element ? '✅ 存在' : '❌ 不存在');
});

// 3. 检查函数是否存在
console.log('3. 检查关键函数:');
const functions = [
    'loadOrderData',
    'updateOrderTable',
    'updateOrderStats',
    'showOrderLoading',
    'showNoOrdersMessage'
];

functions.forEach(funcName => {
    console.log(`   ${funcName}:`, typeof window[funcName] === 'function' ? '✅ 存在' : '❌ 不存在');
});

// 4. 手动测试API调用
console.log('4. 手动测试API调用:');
async function testAPI() {
    try {
        console.log('   正在调用FT订单API...');
        const response = await fetch('/api/v2/orders/ft-summary?page=1&size=5', {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            console.log('   ✅ API调用成功:', data);
            
            // 手动更新页面
            if (typeof updateOrderStats === 'function') {
                updateOrderStats(data.stats || {});
                console.log('   ✅ 统计数据已更新');
            }
            
            if (typeof window.orderData !== 'undefined') {
                window.orderData = data.data || [];
                console.log('   ✅ 订单数据已设置:', window.orderData.length, '条记录');
                
                if (typeof updateOrderTable === 'function') {
                    updateOrderTable();
                    console.log('   ✅ 订单表格已更新');
                }
            }
        } else {
            console.log('   ❌ API调用失败:', response.status, response.statusText);
        }
    } catch (error) {
        console.log('   ❌ API调用错误:', error.message);
    }
}

// 5. 手动调用loadOrderData函数
console.log('5. 手动调用loadOrderData函数:');
if (typeof loadOrderData === 'function') {
    console.log('   正在调用loadOrderData...');
    loadOrderData();
} else {
    console.log('   ❌ loadOrderData函数不存在');
}

// 运行API测试
testAPI();

console.log('🎯 页面功能测试完成');
