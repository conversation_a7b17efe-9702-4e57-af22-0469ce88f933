#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_order_summary_api():
    """测试订单汇总API"""
    
    # 测试FT订单汇总API
    print("测试FT订单汇总API...")
    try:
        response = requests.get('http://localhost:5000/api/v2/orders/ft-summary?page=1&page_size=5')
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text[:500]}...")  # 显示前500个字符
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"成功获取数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            except json.JSONDecodeError as e:
                print(f"JSON解析失败: {e}")
        else:
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # 测试CP订单汇总API
    print("测试CP订单汇总API...")
    try:
        response = requests.get('http://localhost:5000/api/v2/orders/cp-summary?page=1&page_size=5')
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text[:500]}...")  # 显示前500个字符
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"成功获取数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            except json.JSONDecodeError as e:
                print(f"JSON解析失败: {e}")
        else:
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == '__main__':
    test_order_summary_api()
