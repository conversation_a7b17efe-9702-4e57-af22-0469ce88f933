================================================================================
🚀 排产性能优化测试报告
================================================================================

📊 基准性能 (原始算法)
  平均执行时间: 0.00s
  平均数据库查询: 0 次

🎯 优化性能 (优化算法)
  平均执行时间: 1.78s
  平均缓存命中率: 66.7%
  平均数据库查询: 2 次

📈 性能改进指标
  执行时间改进: -86410.1%
  数据库查询减少: -200.0%
  速度提升倍数: 0.0x
  性能等级: ❌ 无明显改进 (No Improvement)

💾 缓存性能
  冷启动加载时间: 0.10s
  热启动加载时间: 0.00s
  缓存效率: 99.0%
  速度提升: 101.9x

🧠 内存使用
  基准内存: 150.4 MB
  数据加载后: 150.4 MB
  峰值内存: 153.5 MB
  数据加载开销: 0.0 MB

📈 可扩展性
  当前批次数量: 408
  推荐算法: heuristic
  算法选择正常: True

================================================================================
测试完成时间: 2025-06-28 22:10:08
================================================================================