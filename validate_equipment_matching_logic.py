#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机台匹配逻辑验证脚本
对比验证数据与我们的排产算法逻辑
"""

import pandas as pd
import numpy as np
from collections import defaultdict
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def analyze_device_handler_mapping():
    """分析DEVICE与HANDLER_ID的映射关系"""
    try:
        # 读取验证数据
        wait_df = pd.read_excel('Excellist2025.06.05/排产验证/et_wait_lot (test).xlsx')
        done_df = pd.read_excel('Excellist2025.06.05/排产验证/lotprioritydone(test).xlsx')
        
        print("=" * 80)
        print("机台匹配逻辑验证报告")
        print("=" * 80)
        
        # 合并数据进行分析
        merged = pd.merge(
            wait_df[['LOT_ID', 'DEVICE', 'STAGE', 'CHIP_ID', 'PKG_PN']],
            done_df[['LOT_ID', 'DEVICE', 'HANDLER_ID', 'PRIORITY']],
            on='LOT_ID',
            suffixes=('_wait', '_done')
        )
        
        print(f"成功匹配的批次数量: {len(merged)}")
        print(f"DEVICE字段一致性: {(merged['DEVICE_wait'] == merged['DEVICE_done']).mean():.2%}")
        
        # 分析DEVICE -> HANDLER_ID的映射规律
        print(f"\n=== DEVICE -> HANDLER_ID 映射分析 ===")
        
        device_handler_mapping = {}
        for _, row in merged.iterrows():
            device = row['DEVICE_wait']
            handler = row['HANDLER_ID']
            stage = row['STAGE']
            
            if device not in device_handler_mapping:
                device_handler_mapping[device] = {
                    'handlers': set(),
                    'stages': set(),
                    'count': 0
                }
            
            device_handler_mapping[device]['handlers'].add(handler)
            device_handler_mapping[device]['stages'].add(stage)
            device_handler_mapping[device]['count'] += 1
        
        # 分析映射规律
        one_to_one_mappings = 0
        one_to_many_mappings = 0
        
        print(f"\n{'DEVICE':<35} {'Handler数量':<12} {'批次数量':<8} {'阶段':<10} {'Handler列表'}")
        print("-" * 120)
        
        for device, info in sorted(device_handler_mapping.items(), key=lambda x: x[1]['count'], reverse=True):
            handler_count = len(info['handlers'])
            batch_count = info['count']
            stages = ', '.join(sorted(info['stages']))
            handlers = ', '.join(sorted(info['handlers']))
            
            if handler_count == 1:
                one_to_one_mappings += 1
                mapping_type = "1:1"
            else:
                one_to_many_mappings += 1
                mapping_type = f"1:{handler_count}"
            
            print(f"{device:<35} {mapping_type:<12} {batch_count:<8} {stages:<10} {handlers}")
        
        print(f"\n映射统计:")
        print(f"  一对一映射 (1个DEVICE -> 1个HANDLER): {one_to_one_mappings}")
        print(f"  一对多映射 (1个DEVICE -> 多个HANDLER): {one_to_many_mappings}")
        print(f"  映射复杂度: {one_to_many_mappings/(one_to_one_mappings+one_to_many_mappings):.2%}")
        
        return merged, device_handler_mapping
        
    except Exception as e:
        print(f"分析出错: {e}")
        return None, None

def analyze_handler_naming_patterns(device_handler_mapping):
    """分析HANDLER_ID的命名规律"""
    print(f"\n=== HANDLER_ID 命名规律分析 ===")
    
    handler_patterns = defaultdict(list)
    
    for device, info in device_handler_mapping.items():
        for handler in info['handlers']:
            # 分析HANDLER_ID的组成部分
            parts = handler.split('-')
            if len(parts) >= 4:
                prefix = parts[0]  # 如 ABAK, LSTI, HCHC 等
                type_code = parts[1]  # 如 O, C 等
                number = parts[2]  # 如 001, 002 等
                suffix = parts[3]  # 如 3004, HEXA 等
                
                pattern = f"{prefix}-{type_code}-XXX-{suffix}"
                handler_patterns[pattern].append({
                    'handler': handler,
                    'device': device,
                    'prefix': prefix,
                    'type_code': type_code,
                    'number': number,
                    'suffix': suffix
                })
    
    print(f"发现的HANDLER命名模式:")
    for pattern, handlers in sorted(handler_patterns.items(), key=lambda x: len(x[1]), reverse=True):
        print(f"  {pattern}: {len(handlers)}个设备")
        
        # 分析每个模式下的设备类型
        devices_in_pattern = set(h['device'] for h in handlers)
        if len(devices_in_pattern) <= 5:  # 只显示前5个设备
            print(f"    设备: {', '.join(sorted(devices_in_pattern))}")
        else:
            print(f"    设备数量: {len(devices_in_pattern)} (部分: {', '.join(list(sorted(devices_in_pattern))[:3])}...)")

def analyze_stage_equipment_relationship(merged):
    """分析STAGE与设备的关系"""
    print(f"\n=== STAGE与设备关系分析 ===")
    
    stage_analysis = defaultdict(lambda: {
        'devices': set(),
        'handlers': set(),
        'count': 0
    })
    
    for _, row in merged.iterrows():
        stage = row['STAGE']
        device = row['DEVICE_wait']
        handler = row['HANDLER_ID']
        
        stage_analysis[stage]['devices'].add(device)
        stage_analysis[stage]['handlers'].add(handler)
        stage_analysis[stage]['count'] += 1
    
    print(f"{'STAGE':<10} {'批次数':<8} {'设备类型数':<10} {'Handler数':<10} {'主要设备类型'}")
    print("-" * 80)
    
    for stage, info in sorted(stage_analysis.items(), key=lambda x: x[1]['count'], reverse=True):
        device_count = len(info['devices'])
        handler_count = len(info['handlers'])
        batch_count = info['count']
        
        # 获取主要设备类型
        main_devices = list(sorted(info['devices']))[:3]
        main_devices_str = ', '.join(main_devices)
        if len(info['devices']) > 3:
            main_devices_str += "..."
        
        print(f"{stage:<10} {batch_count:<8} {device_count:<10} {handler_count:<10} {main_devices_str}")

def validate_our_algorithm_logic():
    """验证我们算法的逻辑是否合理"""
    print(f"\n=== 我们的算法逻辑验证 ===")
    
    # 从验证数据中提取关键信息
    try:
        wait_df = pd.read_excel('Excellist2025.06.05/排产验证/et_wait_lot (test).xlsx')
        done_df = pd.read_excel('Excellist2025.06.05/排产验证/lotprioritydone(test).xlsx')
        
        print(f"✅ 验证数据结构对比:")
        print(f"   待排产数据字段: {len(wait_df.columns)} 个")
        print(f"   已排产数据字段: {len(done_df.columns)} 个")
        print(f"   新增字段: PRIORITY, HANDLER_ID")
        
        # 检查我们的算法是否能处理验证数据中的情况
        print(f"\n✅ 算法适配性检查:")
        
        # 1. DEVICE字段存在性
        if 'DEVICE' in wait_df.columns:
            unique_devices = wait_df['DEVICE'].nunique()
            print(f"   ✓ DEVICE字段存在，包含 {unique_devices} 种不同设备")
        else:
            print(f"   ✗ DEVICE字段缺失！")
        
        # 2. STAGE字段存在性
        if 'STAGE' in wait_df.columns:
            unique_stages = wait_df['STAGE'].nunique()
            stages = wait_df['STAGE'].unique()
            print(f"   ✓ STAGE字段存在，包含 {unique_stages} 种阶段: {', '.join(stages)}")
        else:
            print(f"   ✗ STAGE字段缺失！")
        
        # 3. 关键业务字段
        required_fields = ['LOT_ID', 'CHIP_ID', 'PKG_PN', 'GOOD_QTY', 'RELEASE_TIME']
        missing_fields = [f for f in required_fields if f not in wait_df.columns]
        
        if not missing_fields:
            print(f"   ✓ 所有关键业务字段都存在")
        else:
            print(f"   ⚠️ 缺少字段: {', '.join(missing_fields)}")
        
        # 4. 验证我们的匹配逻辑
        print(f"\n✅ 匹配逻辑验证:")
        
        # 检查DEVICE+STAGE组合的唯一性
        device_stage_combinations = wait_df.groupby(['DEVICE', 'STAGE']).size()
        print(f"   DEVICE+STAGE组合数: {len(device_stage_combinations)}")
        print(f"   平均每组合批次数: {device_stage_combinations.mean():.1f}")
        
        # 检查是否存在我们算法需要的数据源
        print(f"\n✅ 数据源依赖检查:")
        print(f"   需要的数据表:")
        print(f"   - ET_FT_TEST_SPEC: 测试规范 (DEVICE+STAGE -> 配置)")
        print(f"   - EQP_STATUS: 设备状态 (HANDLER_ID -> 可用性)")
        print(f"   - ET_UPH_EQP: UPH数据 (DEVICE+STAGE -> 效率)")
        print(f"   - 配方文件: KIT配置信息")
        
        return True
        
    except Exception as e:
        print(f"验证出错: {e}")
        return False

def generate_recommendations():
    """生成改进建议"""
    print(f"\n=== 改进建议 ===")
    
    print(f"基于验证数据分析，我们的机台匹配逻辑需要以下改进:")
    
    print(f"\n1. 🎯 匹配策略优化:")
    print(f"   - 当前策略: DEVICE+STAGE -> ET_FT_TEST_SPEC -> 配置要求 -> 设备匹配")
    print(f"   - 验证数据显示: 存在1个DEVICE对应多个HANDLER的情况")
    print(f"   - 建议: 增加负载均衡和设备性能评分机制")
    
    print(f"\n2. 🔧 HANDLER_ID分配逻辑:")
    print(f"   - 验证数据中HANDLER_ID命名有规律: PREFIX-TYPE-NUM-SUFFIX")
    print(f"   - 建议: 解析HANDLER_ID结构，按设备类型和能力进行分组")
    print(f"   - 建议: 实现设备族群概念，支持同类设备间的负载分配")
    
    print(f"\n3. 📊 优先级算法:")
    print(f"   - 验证数据优先级范围: 0-77，存在跳跃")
    print(f"   - 建议: 实现更精细的优先级计算，避免优先级冲突")
    print(f"   - 建议: 考虑RELEASE_TIME与优先级的相关性")
    
    print(f"\n4. ⚡ 性能优化:")
    print(f"   - 当前: 每次排产都查询所有配置数据")
    print(f"   - 建议: 实现配置数据缓存机制")
    print(f"   - 建议: 预计算DEVICE->HANDLER映射关系")
    
    print(f"\n5. 🔍 数据完整性:")
    print(f"   - 验证数据缺少DEVICE_NAME字段")
    print(f"   - 建议: 统一DEVICE和DEVICE_NAME的使用")
    print(f"   - 建议: 确保所有必要字段在排产结果中完整保存")

def main():
    """主函数"""
    print("开始验证机台匹配逻辑...")
    
    # 1. 分析设备与处理器的映射关系
    merged, device_handler_mapping = analyze_device_handler_mapping()
    
    if merged is not None and device_handler_mapping is not None:
        # 2. 分析HANDLER命名规律
        analyze_handler_naming_patterns(device_handler_mapping)
        
        # 3. 分析STAGE与设备关系
        analyze_stage_equipment_relationship(merged)
        
        # 4. 验证我们算法的逻辑
        validate_our_algorithm_logic()
        
        # 5. 生成改进建议
        generate_recommendations()
    
    print(f"\n" + "=" * 80)
    print("机台匹配逻辑验证完成！")
    print("=" * 80)

if __name__ == "__main__":
    main() 