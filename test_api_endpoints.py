#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API端点是否正常工作
"""

import requests
import json

def test_api_endpoints():
    """测试API端点"""
    base_url = "http://127.0.0.1:5000"
    
    # 创建会话以保持登录状态
    session = requests.Session()
    
    print("🔍 测试API端点...")
    
    # 1. 测试登录
    print("\n1. 测试登录...")
    login_data = {
        "username": "admin",
        "password": "admin"
    }

    try:
        response = session.post(f"{base_url}/auth/login",
                               data=login_data)  # 使用form data而不是JSON
        
        if response.status_code == 200 or response.status_code == 302:  # 302是重定向，表示登录成功
            print("✅ 登录成功")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            print(f"响应内容: {response.text[:200]}")
            return
    except Exception as e:
        print(f"❌ 登录请求失败: {e}")
        return
    
    # 2. 测试FT订单数据API
    print("\n2. 测试FT订单数据API...")
    try:
        response = session.get(f"{base_url}/api/v2/orders/ft-summary?page=1&size=10")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ FT订单API正常，返回数据: {len(data.get('data', []))}条记录")
            print(f"   成功状态: {data.get('success', False)}")
            if data.get('data'):
                print(f"   第一条记录ID: {data['data'][0].get('id', 'N/A')}")
        else:
            print(f"❌ FT订单API失败: {response.status_code}")
            print(f"响应内容: {response.text[:200]}")
    except Exception as e:
        print(f"❌ FT订单API请求失败: {e}")
    
    # 3. 测试CP订单数据API
    print("\n3. 测试CP订单数据API...")
    try:
        response = session.get(f"{base_url}/api/v2/orders/cp-summary?page=1&size=10")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ CP订单API正常，返回数据: {len(data.get('data', []))}条记录")
            print(f"   成功状态: {data.get('success', False)}")
            if data.get('data'):
                print(f"   第一条记录ID: {data['data'][0].get('id', 'N/A')}")
        else:
            print(f"❌ CP订单API失败: {response.status_code}")
            print(f"响应内容: {response.text[:200]}")
    except Exception as e:
        print(f"❌ CP订单API请求失败: {e}")
    
    # 4. 测试邮箱配置API
    print("\n4. 测试邮箱配置API...")
    try:
        response = session.get(f"{base_url}/api/email_configs")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 邮箱配置API正常")
            print(f"   成功状态: {data.get('status', 'unknown')}")
            print(f"   配置数量: {len(data.get('data', []))}")
        else:
            print(f"❌ 邮箱配置API失败: {response.status_code}")
            print(f"响应内容: {response.text[:200]}")
    except Exception as e:
        print(f"❌ 邮箱配置API请求失败: {e}")
    
    # 5. 测试导出API
    print("\n5. 测试导出API...")
    try:
        response = session.get(f"{base_url}/api/v2/orders/ft-summary/export")
        
        if response.status_code == 200:
            content_type = response.headers.get('content-type', '')
            if 'excel' in content_type or 'spreadsheet' in content_type:
                print(f"✅ 导出API正常，返回Excel文件")
                print(f"   文件大小: {len(response.content)} bytes")
            else:
                print(f"❌ 导出API返回了非Excel内容: {content_type}")
        else:
            print(f"❌ 导出API失败: {response.status_code}")
            print(f"响应内容: {response.text[:200]}")
    except Exception as e:
        print(f"❌ 导出API请求失败: {e}")
    
    print("\n🎯 API测试完成")

if __name__ == "__main__":
    test_api_endpoints()
