#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速API测试
"""

import requests
import json
import time

def test_api():
    """测试API"""
    session = requests.Session()
    
    # 登录
    print("🔐 尝试登录...")
    login_data = {'username': 'admin', 'password': 'admin'}
    
    try:
        login_resp = session.post('http://127.0.0.1:5000/auth/login', data=login_data)
        print(f"登录状态: {login_resp.status_code}")
        
        if login_resp.status_code not in [200, 302]:
            print("❌ 登录失败")
            return
        
        print("✅ 登录成功")
        
        # 测试策略列表API
        print("\n📋 测试策略列表API...")
        strategies_resp = session.get('http://127.0.0.1:5000/api/production/algorithm-strategies')
        print(f"策略列表状态: {strategies_resp.status_code}")
        
        if strategies_resp.status_code == 200:
            strategies_data = strategies_resp.json()
            print(f"策略数量: {len(strategies_data.get('strategies', []))}")
            for strategy in strategies_data.get('strategies', []):
                print(f"  - {strategy['value']}: {strategy['label']}")
        
        # 测试权重API
        strategies = ['intelligent', 'deadline', 'product', 'value']
        
        for strategy in strategies:
            print(f"\n📊 测试 {strategy} 策略权重...")
            weights_resp = session.get(f'http://127.0.0.1:5000/api/production/algorithm-weights?strategy={strategy}')
            print(f"权重API状态: {weights_resp.status_code}")
            
            if weights_resp.status_code == 200:
                weights_data = weights_resp.json()
                if weights_data.get('success'):
                    weights = weights_data.get('weights', {})
                    print(f"  技术匹配度: {weights.get('tech_match_weight', 0)}%")
                    print(f"  负载均衡: {weights.get('load_balance_weight', 0)}%")
                    print(f"  交期紧迫度: {weights.get('deadline_weight', 0)}%")
                    print(f"  产值效率: {weights.get('value_efficiency_weight', 0)}%")
                    print(f"  业务优先级: {weights.get('business_priority_weight', 0)}%")
                else:
                    print(f"  ❌ API返回失败: {weights_data.get('message', '未知错误')}")
            else:
                print(f"  ❌ HTTP错误: {weights_resp.status_code}")
        
        print("\n✅ API测试完成")
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    test_api()
