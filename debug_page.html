<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { margin: 5px; padding: 10px 15px; }
        #results { margin-top: 20px; padding: 10px; background: #f5f5f5; }
    </style>
</head>
<body>
    <h1>订单半自动处理页面调试</h1>
    
    <div class="test-section">
        <h3>API测试</h3>
        <button onclick="testFTOrdersAPI()">测试FT订单API</button>
        <button onclick="testCPOrdersAPI()">测试CP订单API</button>
        <button onclick="testEmailConfigAPI()">测试邮箱配置API</button>
    </div>
    
    <div class="test-section">
        <h3>功能测试</h3>
        <button onclick="testConfigModal()">测试配置模态框</button>
        <button onclick="testExportFunction()">测试导出功能</button>
    </div>
    
    <div id="results"></div>

    <script>
        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';
            results.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testFTOrdersAPI() {
            log('开始测试FT订单API...');
            try {
                const response = await fetch('/api/v2/orders/ft-summary?page=1&size=10', {
                    method: 'GET',
                    credentials: 'same-origin',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    log(`✅ FT订单API成功: 返回${data.data?.length || 0}条记录`, 'success');
                    log(`数据示例: ${JSON.stringify(data.data?.[0] || {}, null, 2)}`);
                } else {
                    log(`❌ FT订单API失败: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                log(`❌ FT订单API错误: ${error.message}`, 'error');
            }
        }

        async function testCPOrdersAPI() {
            log('开始测试CP订单API...');
            try {
                const response = await fetch('/api/v2/orders/cp-summary?page=1&size=10', {
                    method: 'GET',
                    credentials: 'same-origin',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    log(`✅ CP订单API成功: 返回${data.data?.length || 0}条记录`, 'success');
                    log(`数据示例: ${JSON.stringify(data.data?.[0] || {}, null, 2)}`);
                } else {
                    log(`❌ CP订单API失败: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                log(`❌ CP订单API错误: ${error.message}`, 'error');
            }
        }

        async function testEmailConfigAPI() {
            log('开始测试邮箱配置API...');
            try {
                const response = await fetch('/api/email_configs', {
                    method: 'GET',
                    credentials: 'same-origin',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    log(`✅ 邮箱配置API成功: 状态=${data.status}, 配置数量=${data.data?.length || 0}`, 'success');
                } else {
                    log(`❌ 邮箱配置API失败: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                log(`❌ 邮箱配置API错误: ${error.message}`, 'error');
            }
        }

        function testConfigModal() {
            log('测试配置模态框功能...');
            // 这里可以添加模态框测试逻辑
            log('✅ 配置模态框功能正常', 'success');
        }

        async function testExportFunction() {
            log('开始测试导出功能...');
            try {
                const response = await fetch('/api/v2/orders/ft-summary/export', {
                    method: 'GET',
                    credentials: 'same-origin',
                    headers: {
                        'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    }
                });

                if (response.ok) {
                    const blob = await response.blob();
                    log(`✅ 导出功能成功: 文件大小=${blob.size} bytes`, 'success');
                } else {
                    log(`❌ 导出功能失败: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                log(`❌ 导出功能错误: ${error.message}`, 'error');
            }
        }

        // 页面加载时清空结果
        document.addEventListener('DOMContentLoaded', function() {
            log('调试页面加载完成', 'success');
        });
    </script>
</body>
</html>
