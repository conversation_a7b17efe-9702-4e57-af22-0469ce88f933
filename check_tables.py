#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql

try:
    conn = pymysql.connect(
        host='localhost',
        user='root',
        password='WWWwww123!',
        database='aps',
        charset='utf8mb4'
    )
    cursor = conn.cursor()
    
    # 检查表是否存在
    cursor.execute("SHOW TABLES LIKE 'ft_order_summary'")
    ft_table = cursor.fetchone()
    
    cursor.execute("SHOW TABLES LIKE 'cp_order_summary'")
    cp_table = cursor.fetchone()
    
    print('FT订单汇总表存在:', bool(ft_table))
    print('CP订单汇总表存在:', bool(cp_table))
    
    if ft_table:
        cursor.execute('SELECT COUNT(*) FROM ft_order_summary')
        ft_count = cursor.fetchone()[0]
        print(f'FT订单汇总表记录数: {ft_count}')
    
    if cp_table:
        cursor.execute('SELECT COUNT(*) FROM cp_order_summary')
        cp_count = cursor.fetchone()[0]
        print(f'CP订单汇总表记录数: {cp_count}')
    
    # 检查邮箱配置表
    cursor.execute("SHOW TABLES LIKE 'email_configs'")
    email_table = cursor.fetchone()
    print('邮箱配置表存在:', bool(email_table))
    
    if email_table:
        cursor.execute('SELECT COUNT(*) FROM email_configs')
        email_count = cursor.fetchone()[0]
        print(f'邮箱配置记录数: {email_count}')
    
    cursor.close()
    conn.close()
    
except Exception as e:
    print(f'数据库连接错误: {e}')
