#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_with_login():
    """测试带登录的API"""
    
    # 创建会话
    session = requests.Session()
    
    # 1. 先登录
    print("1. 尝试登录...")
    login_data = {
        'username': 'admin',
        'password': 'admin'
    }
    
    login_response = session.post('http://localhost:5000/auth/login', data=login_data)
    print(f"登录状态码: {login_response.status_code}")
    
    if login_response.status_code != 200:
        print("登录失败")
        return
    
    # 2. 测试邮箱配置API
    print("\n2. 测试邮箱配置API...")
    try:
        response = session.get('http://localhost:5000/api/email_configs')
        print(f"邮箱配置API状态码: {response.status_code}")
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"邮箱配置数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            except json.JSONDecodeError:
                print(f"响应内容: {response.text[:200]}...")
        else:
            print(f"错误响应: {response.text[:200]}...")
    except Exception as e:
        print(f"邮箱配置API请求失败: {e}")
    
    # 3. 测试FT订单汇总API
    print("\n3. 测试FT订单汇总API...")
    try:
        response = session.get('http://localhost:5000/api/v2/orders/ft-summary?page=1&page_size=5')
        print(f"FT订单API状态码: {response.status_code}")
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"FT订单数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            except json.JSONDecodeError:
                print(f"响应内容: {response.text[:200]}...")
        else:
            print(f"错误响应: {response.text[:200]}...")
    except Exception as e:
        print(f"FT订单API请求失败: {e}")
    
    # 4. 测试CP订单汇总API
    print("\n4. 测试CP订单汇总API...")
    try:
        response = session.get('http://localhost:5000/api/v2/orders/cp-summary?page=1&page_size=5')
        print(f"CP订单API状态码: {response.status_code}")
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"CP订单数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            except json.JSONDecodeError:
                print(f"响应内容: {response.text[:200]}...")
        else:
            print(f"错误响应: {response.text[:200]}...")
    except Exception as e:
        print(f"CP订单API请求失败: {e}")

if __name__ == '__main__':
    test_with_login()
