#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
排产结果验证分析
对比数据库中的lotprioritydone表与验证文件lotprioritydone(test).xlsx的差异
"""

import pandas as pd
import pymysql
import json
from datetime import datetime
from typing import Dict, List, Tuple, Any
from collections import defaultdict, Counter
import numpy as np

class SchedulingVerificationAnalyzer:
    """排产结果验证分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.db_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'WWWwww123!',
            'database': 'aps',
            'charset': 'utf8mb4'
        }
        
        self.verification_file = 'Excellist2025.06.05/排产验证/lotprioritydone(test).xlsx'
        
        self.analysis_results = {}
        
    def load_database_results(self) -> pd.DataFrame:
        """从数据库加载排产结果"""
        print("📊 从数据库加载排产结果...")
        
        try:
            connection = pymysql.connect(**self.db_config)
            
            # 查询数据库中的排产结果
            query = """
            SELECT 
                id, PRIORITY, HANDLER_ID, LOT_ID, LOT_TYPE, GOOD_QTY, 
                PROD_ID, DEVICE, CHIP_ID, PKG_PN, PO_ID, STAGE, 
                WIP_STATE, PROC_STATE, HOLD_STATE, FLOW_ID, FLOW_VER, 
                RELEASE_TIME, FAC_ID, CREATE_TIME,
                match_type, comprehensive_score, processing_time, 
                changeover_time, algorithm_version
            FROM lotprioritydone 
            ORDER BY CREATE_TIME DESC
            """
            
            db_df = pd.read_sql(query, connection)
            connection.close()
            
            print(f"✅ 数据库加载完成: {len(db_df)} 条记录")
            
            # 数据类型转换和清理
            if not db_df.empty:
                # 处理可能的NULL值
                db_df = db_df.fillna('')
                
                # 确保数值字段的类型正确
                numeric_fields = ['PRIORITY', 'GOOD_QTY', 'comprehensive_score', 'processing_time', 'changeover_time']
                for field in numeric_fields:
                    if field in db_df.columns:
                        db_df[field] = pd.to_numeric(db_df[field], errors='coerce').fillna(0)
            
            return db_df
            
        except Exception as e:
            print(f"❌ 数据库加载失败: {e}")
            return pd.DataFrame()
    
    def load_verification_results(self) -> pd.DataFrame:
        """加载验证文件结果"""
        print("📋 加载验证文件结果...")
        
        try:
            verification_df = pd.read_excel(self.verification_file)
            print(f"✅ 验证文件加载完成: {len(verification_df)} 条记录")
            
            # 数据清理
            verification_df = verification_df.fillna('')
            
            # 确保数值字段的类型正确
            if 'PRIORITY' in verification_df.columns:
                verification_df['PRIORITY'] = pd.to_numeric(verification_df['PRIORITY'], errors='coerce').fillna(0)
            if 'GOOD_QTY' in verification_df.columns:
                verification_df['GOOD_QTY'] = pd.to_numeric(verification_df['GOOD_QTY'], errors='coerce').fillna(0)
            
            return verification_df
            
        except Exception as e:
            print(f"❌ 验证文件加载失败: {e}")
            return pd.DataFrame()
    
    def analyze_data_completeness(self, db_df: pd.DataFrame, verification_df: pd.DataFrame) -> Dict:
        """分析数据完整性"""
        print("🔍 分析数据完整性...")
        
        completeness_analysis = {
            'record_counts': {
                'database': len(db_df),
                'verification_file': len(verification_df),
                'difference': len(db_df) - len(verification_df)
            },
            'lot_id_analysis': {},
            'missing_records': {},
            'extra_records': {}
        }
        
        if not db_df.empty and not verification_df.empty:
            # LOT_ID分析
            db_lots = set(db_df['LOT_ID'].unique()) if 'LOT_ID' in db_df.columns else set()
            verification_lots = set(verification_df['LOT_ID'].unique()) if 'LOT_ID' in verification_df.columns else set()
            
            missing_lots = verification_lots - db_lots
            extra_lots = db_lots - verification_lots
            common_lots = db_lots & verification_lots
            
            completeness_analysis['lot_id_analysis'] = {
                'database_lots': len(db_lots),
                'verification_lots': len(verification_lots),
                'common_lots': len(common_lots),
                'missing_lots': len(missing_lots),
                'extra_lots': len(extra_lots),
                'missing_lot_list': list(missing_lots)[:10],  # 只显示前10个
                'extra_lot_list': list(extra_lots)[:10]
            }
            
            # 详细记录分析
            if 'LOT_ID' in db_df.columns and 'STAGE' in db_df.columns:
                db_keys = set(zip(db_df['LOT_ID'], db_df['STAGE']))
            else:
                db_keys = set()
            
            if 'LOT_ID' in verification_df.columns and 'STAGE' in verification_df.columns:
                verification_keys = set(zip(verification_df['LOT_ID'], verification_df['STAGE']))
            else:
                verification_keys = set()
            
            missing_records = verification_keys - db_keys
            extra_records = db_keys - verification_keys
            
            completeness_analysis['missing_records'] = {
                'count': len(missing_records),
                'examples': list(missing_records)[:10]
            }
            
            completeness_analysis['extra_records'] = {
                'count': len(extra_records),
                'examples': list(extra_records)[:10]
            }
        
        return completeness_analysis
    
    def analyze_field_differences(self, db_df: pd.DataFrame, verification_df: pd.DataFrame) -> Dict:
        """分析字段差异"""
        print("🔍 分析字段差异...")
        
        field_analysis = {
            'column_comparison': {},
            'field_accuracy': {},
            'detailed_differences': []
        }
        
        if db_df.empty or verification_df.empty:
            return field_analysis
        
        # 列对比
        db_columns = set(db_df.columns)
        verification_columns = set(verification_df.columns)
        
        common_columns = db_columns & verification_columns
        db_only_columns = db_columns - verification_columns
        verification_only_columns = verification_columns - db_columns
        
        field_analysis['column_comparison'] = {
            'database_columns': list(db_columns),
            'verification_columns': list(verification_columns),
            'common_columns': list(common_columns),
            'database_only': list(db_only_columns),
            'verification_only': list(verification_only_columns)
        }
        
        # 创建索引进行详细对比
        if 'LOT_ID' in common_columns and 'STAGE' in common_columns:
            # 创建数据索引
            db_index = {}
            for _, row in db_df.iterrows():
                key = (row['LOT_ID'], row['STAGE'])
                db_index[key] = row.to_dict()
            
            verification_index = {}
            for _, row in verification_df.iterrows():
                key = (row['LOT_ID'], row['STAGE'])
                verification_index[key] = row.to_dict()
            
            # 找到共同记录进行字段对比
            common_keys = set(db_index.keys()) & set(verification_index.keys())
            
            # 关键字段对比
            key_fields = ['LOT_ID', 'DEVICE', 'STAGE', 'HANDLER_ID', 'PRIORITY', 'GOOD_QTY']
            field_matches = {field: 0 for field in key_fields if field in common_columns}
            field_total = len(common_keys)
            
            detailed_differences = []
            
            for key in common_keys:
                db_record = db_index[key]
                verification_record = verification_index[key]
                
                record_differences = {}
                
                for field in key_fields:
                    if field in common_columns:
                        db_value = db_record.get(field, '')
                        verification_value = verification_record.get(field, '')
                        
                        # 特殊处理数值字段
                        if field in ['PRIORITY', 'GOOD_QTY']:
                            try:
                                db_value = float(db_value) if db_value != '' else 0
                                verification_value = float(verification_value) if verification_value != '' else 0
                            except:
                                pass
                        
                        if db_value == verification_value:
                            field_matches[field] += 1
                        else:
                            record_differences[field] = {
                                'database': db_value,
                                'verification': verification_value
                            }
                
                if record_differences:
                    detailed_differences.append({
                        'lot_id': key[0],
                        'stage': key[1],
                        'differences': record_differences
                    })
            
            # 计算字段准确率
            for field in field_matches:
                accuracy = field_matches[field] / field_total * 100 if field_total > 0 else 0
                field_analysis['field_accuracy'][field] = {
                    'matches': field_matches[field],
                    'total': field_total,
                    'accuracy_percentage': round(accuracy, 2)
                }
            
            field_analysis['detailed_differences'] = detailed_differences[:50]  # 限制显示前50个差异
        
        return field_analysis
    
    def analyze_handler_selection_differences(self, db_df: pd.DataFrame, verification_df: pd.DataFrame) -> Dict:
        """分析HANDLER选择差异"""
        print("🔍 分析HANDLER选择差异...")
        
        handler_analysis = {
            'handler_distribution': {},
            'selection_differences': [],
            'device_stage_mapping_comparison': {}
        }
        
        if db_df.empty or verification_df.empty:
            return handler_analysis
        
        # HANDLER分布对比
        if 'HANDLER_ID' in db_df.columns:
            db_handler_dist = db_df['HANDLER_ID'].value_counts().to_dict()
        else:
            db_handler_dist = {}
        
        if 'HANDLER_ID' in verification_df.columns:
            verification_handler_dist = verification_df['HANDLER_ID'].value_counts().to_dict()
        else:
            verification_handler_dist = {}
        
        handler_analysis['handler_distribution'] = {
            'database': db_handler_dist,
            'verification': verification_handler_dist
        }
        
        # HANDLER选择差异分析
        if all(col in db_df.columns for col in ['LOT_ID', 'STAGE', 'DEVICE', 'HANDLER_ID']) and \
           all(col in verification_df.columns for col in ['LOT_ID', 'STAGE', 'DEVICE', 'HANDLER_ID']):
            
            # 创建索引
            db_handler_map = {}
            for _, row in db_df.iterrows():
                key = (row['LOT_ID'], row['STAGE'], row['DEVICE'])
                db_handler_map[key] = row['HANDLER_ID']
            
            verification_handler_map = {}
            for _, row in verification_df.iterrows():
                key = (row['LOT_ID'], row['STAGE'], row['DEVICE'])
                verification_handler_map[key] = row['HANDLER_ID']
            
            # 找出HANDLER选择差异
            selection_differences = []
            common_keys = set(db_handler_map.keys()) & set(verification_handler_map.keys())
            
            for key in common_keys:
                db_handler = db_handler_map[key]
                verification_handler = verification_handler_map[key]
                
                if db_handler != verification_handler:
                    selection_differences.append({
                        'lot_id': key[0],
                        'stage': key[1],
                        'device': key[2],
                        'database_handler': db_handler,
                        'verification_handler': verification_handler
                    })
            
            handler_analysis['selection_differences'] = selection_differences[:30]  # 限制显示前30个
            
            # DEVICE+STAGE映射对比
            device_stage_mapping = {}
            
            # 数据库映射
            db_device_stage_handlers = defaultdict(set)
            for _, row in db_df.iterrows():
                key = (row['DEVICE'], row['STAGE'])
                db_device_stage_handlers[key].add(row['HANDLER_ID'])
            
            # 验证文件映射
            verification_device_stage_handlers = defaultdict(set)
            for _, row in verification_df.iterrows():
                key = (row['DEVICE'], row['STAGE'])
                verification_device_stage_handlers[key].add(row['HANDLER_ID'])
            
            # 对比映射差异
            all_device_stage_keys = set(db_device_stage_handlers.keys()) | set(verification_device_stage_handlers.keys())
            
            for key in all_device_stage_keys:
                db_handlers = db_device_stage_handlers.get(key, set())
                verification_handlers = verification_device_stage_handlers.get(key, set())
                
                device_stage_mapping[f"{key[0]}+{key[1]}"] = {
                    'database_handlers': list(db_handlers),
                    'verification_handlers': list(verification_handlers),
                    'handlers_match': db_handlers == verification_handlers
                }
            
            handler_analysis['device_stage_mapping_comparison'] = device_stage_mapping
        
        return handler_analysis
    
    def analyze_priority_sequence(self, db_df: pd.DataFrame, verification_df: pd.DataFrame) -> Dict:
        """分析PRIORITY序列差异"""
        print("🔍 分析PRIORITY序列差异...")
        
        priority_analysis = {
            'sequence_validation': {},
            'priority_differences': [],
            'handler_priority_comparison': {}
        }
        
        if db_df.empty or verification_df.empty:
            return priority_analysis
        
        # PRIORITY序列验证
        if 'HANDLER_ID' in db_df.columns and 'PRIORITY' in db_df.columns:
            db_priority_issues = []
            for handler_id in db_df['HANDLER_ID'].unique():
                handler_data = db_df[db_df['HANDLER_ID'] == handler_id]['PRIORITY'].sort_values()
                expected_sequence = list(range(1, len(handler_data) + 1))
                actual_sequence = handler_data.tolist()
                
                if actual_sequence != expected_sequence:
                    db_priority_issues.append({
                        'handler_id': handler_id,
                        'expected': expected_sequence,
                        'actual': actual_sequence
                    })
            
            priority_analysis['sequence_validation']['database_issues'] = db_priority_issues
        
        if 'HANDLER_ID' in verification_df.columns and 'PRIORITY' in verification_df.columns:
            verification_priority_issues = []
            for handler_id in verification_df['HANDLER_ID'].unique():
                handler_data = verification_df[verification_df['HANDLER_ID'] == handler_id]['PRIORITY'].sort_values()
                expected_sequence = list(range(1, len(handler_data) + 1))
                actual_sequence = handler_data.tolist()
                
                if actual_sequence != expected_sequence:
                    verification_priority_issues.append({
                        'handler_id': handler_id,
                        'expected': expected_sequence,
                        'actual': actual_sequence
                    })
            
            priority_analysis['sequence_validation']['verification_issues'] = verification_priority_issues
        
        # PRIORITY对比分析
        if all(col in db_df.columns for col in ['LOT_ID', 'STAGE', 'PRIORITY']) and \
           all(col in verification_df.columns for col in ['LOT_ID', 'STAGE', 'PRIORITY']):
            
            # 创建索引
            db_priority_map = {}
            for _, row in db_df.iterrows():
                key = (row['LOT_ID'], row['STAGE'])
                db_priority_map[key] = row['PRIORITY']
            
            verification_priority_map = {}
            for _, row in verification_df.iterrows():
                key = (row['LOT_ID'], row['STAGE'])
                verification_priority_map[key] = row['PRIORITY']
            
            # 找出PRIORITY差异
            priority_differences = []
            common_keys = set(db_priority_map.keys()) & set(verification_priority_map.keys())
            
            for key in common_keys:
                db_priority = db_priority_map[key]
                verification_priority = verification_priority_map[key]
                
                if db_priority != verification_priority:
                    priority_differences.append({
                        'lot_id': key[0],
                        'stage': key[1],
                        'database_priority': db_priority,
                        'verification_priority': verification_priority
                    })
            
            priority_analysis['priority_differences'] = priority_differences[:30]  # 限制显示前30个
        
        return priority_analysis
    
    def generate_analysis_report(self, 
                               completeness_analysis: Dict,
                               field_analysis: Dict,
                               handler_analysis: Dict,
                               priority_analysis: Dict) -> str:
        """生成分析报告"""
        
        report_lines = []
        report_lines.append("# 排产结果验证分析报告")
        report_lines.append("")
        report_lines.append(f"**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"**数据源**: 数据库 `lotprioritydone` vs 验证文件 `lotprioritydone(test).xlsx`")
        report_lines.append("")
        
        # 1. 数据完整性分析
        report_lines.append("## 1. 数据完整性分析")
        report_lines.append("")
        
        record_counts = completeness_analysis.get('record_counts', {})
        report_lines.append("### 记录数量对比")
        report_lines.append(f"- **数据库记录数**: {record_counts.get('database', 0)}")
        report_lines.append(f"- **验证文件记录数**: {record_counts.get('verification_file', 0)}")
        report_lines.append(f"- **差异**: {record_counts.get('difference', 0)}")
        report_lines.append("")
        
        lot_analysis = completeness_analysis.get('lot_id_analysis', {})
        if lot_analysis:
            report_lines.append("### LOT_ID完整性分析")
            report_lines.append(f"- **数据库LOT数量**: {lot_analysis.get('database_lots', 0)}")
            report_lines.append(f"- **验证文件LOT数量**: {lot_analysis.get('verification_lots', 0)}")
            report_lines.append(f"- **共同LOT数量**: {lot_analysis.get('common_lots', 0)}")
            report_lines.append(f"- **缺失LOT数量**: {lot_analysis.get('missing_lots', 0)}")
            report_lines.append(f"- **多余LOT数量**: {lot_analysis.get('extra_lots', 0)}")
            
            if lot_analysis.get('missing_lot_list'):
                report_lines.append("")
                report_lines.append("**缺失LOT示例**:")
                for lot in lot_analysis['missing_lot_list']:
                    report_lines.append(f"- {lot}")
            
            if lot_analysis.get('extra_lot_list'):
                report_lines.append("")
                report_lines.append("**多余LOT示例**:")
                for lot in lot_analysis['extra_lot_list']:
                    report_lines.append(f"- {lot}")
        
        report_lines.append("")
        
        # 2. 字段差异分析
        report_lines.append("## 2. 字段差异分析")
        report_lines.append("")
        
        column_comparison = field_analysis.get('column_comparison', {})
        if column_comparison:
            report_lines.append("### 列结构对比")
            report_lines.append(f"- **数据库列数**: {len(column_comparison.get('database_columns', []))}")
            report_lines.append(f"- **验证文件列数**: {len(column_comparison.get('verification_columns', []))}")
            report_lines.append(f"- **共同列数**: {len(column_comparison.get('common_columns', []))}")
            
            if column_comparison.get('database_only'):
                report_lines.append("")
                report_lines.append("**数据库独有列**:")
                for col in column_comparison['database_only']:
                    report_lines.append(f"- {col}")
            
            if column_comparison.get('verification_only'):
                report_lines.append("")
                report_lines.append("**验证文件独有列**:")
                for col in column_comparison['verification_only']:
                    report_lines.append(f"- {col}")
        
        field_accuracy = field_analysis.get('field_accuracy', {})
        if field_accuracy:
            report_lines.append("")
            report_lines.append("### 关键字段准确性")
            report_lines.append("| 字段 | 匹配数 | 总数 | 准确率 |")
            report_lines.append("|------|--------|------|--------|")
            
            for field, accuracy in field_accuracy.items():
                matches = accuracy.get('matches', 0)
                total = accuracy.get('total', 0)
                percentage = accuracy.get('accuracy_percentage', 0)
                report_lines.append(f"| {field} | {matches} | {total} | {percentage}% |")
        
        # 3. HANDLER选择差异分析
        report_lines.append("")
        report_lines.append("## 3. HANDLER选择差异分析")
        report_lines.append("")
        
        selection_differences = handler_analysis.get('selection_differences', [])
        if selection_differences:
            report_lines.append(f"### HANDLER选择差异 (共{len(selection_differences)}个)")
            report_lines.append("")
            report_lines.append("| LOT_ID | STAGE | DEVICE | 数据库HANDLER | 验证文件HANDLER |")
            report_lines.append("|--------|-------|--------|---------------|-----------------|")
            
            for diff in selection_differences[:20]:  # 只显示前20个
                report_lines.append(f"| {diff['lot_id']} | {diff['stage']} | {diff['device']} | {diff['database_handler']} | {diff['verification_handler']} |")
        
        # 4. PRIORITY序列分析
        report_lines.append("")
        report_lines.append("## 4. PRIORITY序列分析")
        report_lines.append("")
        
        sequence_validation = priority_analysis.get('sequence_validation', {})
        db_issues = sequence_validation.get('database_issues', [])
        verification_issues = sequence_validation.get('verification_issues', [])
        
        report_lines.append(f"- **数据库PRIORITY序列问题**: {len(db_issues)}个HANDLER")
        report_lines.append(f"- **验证文件PRIORITY序列问题**: {len(verification_issues)}个HANDLER")
        
        priority_differences = priority_analysis.get('priority_differences', [])
        if priority_differences:
            report_lines.append("")
            report_lines.append(f"### PRIORITY值差异 (共{len(priority_differences)}个)")
            report_lines.append("")
            report_lines.append("| LOT_ID | STAGE | 数据库PRIORITY | 验证文件PRIORITY |")
            report_lines.append("|--------|-------|----------------|------------------|")
            
            for diff in priority_differences[:15]:  # 只显示前15个
                report_lines.append(f"| {diff['lot_id']} | {diff['stage']} | {diff['database_priority']} | {diff['verification_priority']} |")
        
        # 5. 总结和建议
        report_lines.append("")
        report_lines.append("## 5. 总结和建议")
        report_lines.append("")
        
        # 计算总体匹配率
        if field_accuracy:
            avg_accuracy = sum(acc['accuracy_percentage'] for acc in field_accuracy.values()) / len(field_accuracy)
            
            if avg_accuracy >= 95:
                status = "🎉 优秀"
            elif avg_accuracy >= 85:
                status = "✅ 良好"
            elif avg_accuracy >= 70:
                status = "⚠️ 一般"
            else:
                status = "❌ 需要改进"
            
            report_lines.append(f"### 总体评估: {status}")
            report_lines.append(f"- **平均字段准确率**: {avg_accuracy:.1f}%")
        
        report_lines.append("")
        report_lines.append("### 主要发现")
        
        # 基于分析结果生成发现
        findings = []
        
        if record_counts.get('difference', 0) == 0:
            findings.append("✅ 记录数量完全匹配")
        else:
            findings.append(f"⚠️ 记录数量存在差异: {record_counts.get('difference', 0)}条")
        
        if lot_analysis.get('missing_lots', 0) == 0 and lot_analysis.get('extra_lots', 0) == 0:
            findings.append("✅ LOT_ID完整性良好")
        else:
            findings.append(f"⚠️ LOT_ID存在差异: 缺失{lot_analysis.get('missing_lots', 0)}个, 多余{lot_analysis.get('extra_lots', 0)}个")
        
        if len(selection_differences) == 0:
            findings.append("✅ HANDLER选择完全一致")
        else:
            findings.append(f"⚠️ HANDLER选择存在{len(selection_differences)}个差异")
        
        if len(priority_differences) == 0:
            findings.append("✅ PRIORITY分配完全一致")
        else:
            findings.append(f"⚠️ PRIORITY分配存在{len(priority_differences)}个差异")
        
        for finding in findings:
            report_lines.append(f"- {finding}")
        
        report_lines.append("")
        report_lines.append("### 改进建议")
        
        suggestions = []
        
        if len(selection_differences) > 0:
            suggestions.append("优化HANDLER选择算法，考虑设备性能和负载均衡")
        
        if len(priority_differences) > 0:
            suggestions.append("检查PRIORITY分配逻辑，确保序列连续性")
        
        if record_counts.get('difference', 0) != 0:
            suggestions.append("检查数据导入和处理流程，确保数据完整性")
        
        if not suggestions:
            suggestions.append("当前排产算法表现良好，可考虑进一步优化细节")
        
        for suggestion in suggestions:
            report_lines.append(f"- {suggestion}")
        
        report_lines.append("")
        report_lines.append("---")
        report_lines.append(f"*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*")
        
        return "\n".join(report_lines)
    
    def run_complete_analysis(self):
        """运行完整分析"""
        print("🚀 开始排产结果验证分析...")
        
        try:
            # 1. 加载数据
            db_df = self.load_database_results()
            verification_df = self.load_verification_results()
            
            if db_df.empty:
                print("❌ 数据库数据为空，无法进行分析")
                return
            
            if verification_df.empty:
                print("❌ 验证文件数据为空，无法进行分析")
                return
            
            # 2. 执行各项分析
            completeness_analysis = self.analyze_data_completeness(db_df, verification_df)
            field_analysis = self.analyze_field_differences(db_df, verification_df)
            handler_analysis = self.analyze_handler_selection_differences(db_df, verification_df)
            priority_analysis = self.analyze_priority_sequence(db_df, verification_df)
            
            # 3. 生成报告
            report_content = self.generate_analysis_report(
                completeness_analysis, field_analysis, handler_analysis, priority_analysis
            )
            
            # 4. 保存报告
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_file = f'scheduling_verification_analysis_{timestamp}.md'
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            print(f"✅ 分析报告已生成: {report_file}")
            
            # 5. 保存详细数据
            analysis_data = {
                'timestamp': timestamp,
                'completeness_analysis': completeness_analysis,
                'field_analysis': field_analysis,
                'handler_analysis': handler_analysis,
                'priority_analysis': priority_analysis
            }
            
            data_file = f'scheduling_verification_data_{timestamp}.json'
            with open(data_file, 'w', encoding='utf-8') as f:
                json.dump(analysis_data, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"✅ 详细数据已保存: {data_file}")
            
            # 6. 显示报告内容
            print("\n" + "="*80)
            print("分析报告预览:")
            print("="*80)
            print(report_content)
            
        except Exception as e:
            print(f"❌ 分析失败: {e}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    analyzer = SchedulingVerificationAnalyzer()
    analyzer.run_complete_analysis()

if __name__ == "__main__":
    main() 