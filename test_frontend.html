<!DOCTYPE html>
<html>
<head>
    <title>测试前端API调用</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>测试算法权重API</h1>
    <div id="results"></div>
    
    <script>
    function testAPI() {
        console.log('开始测试API...');
        
        $.getJSON('/api/production/algorithm-weights?strategy=intelligent', function(resp) {
            console.log('API响应:', resp);
            $('#results').html('<pre>' + JSON.stringify(resp, null, 2) + '</pre>');
            
            if (resp.success) {
                var w = resp.weights;
                console.log('权重数据:', w);
                
                // 测试字段映射
                var fields = ['tech_match_weight', 'load_balance_weight', 'deadline_weight', 'value_efficiency_weight', 'business_priority_weight'];
                fields.forEach(function(field) {
                    console.log('字段 ' + field + ':', w[field]);
                });
            }
        }).fail(function(xhr, status, error) {
            console.error('API调用失败:', status, error);
            $('#results').html('API调用失败: ' + status + ' - ' + error);
        });
    }
    
    // 页面加载后立即测试
    $(document).ready(function() {
        testAPI();
    });
    </script>
</body>
</html>
