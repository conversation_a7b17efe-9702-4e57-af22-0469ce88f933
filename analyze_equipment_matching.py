#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设备匹配详细分析脚本
重点分析DEVICE字段和DEVICE_NAME字段的差异
"""

import pandas as pd
import numpy as np

def detailed_field_analysis():
    """详细分析字段差异"""
    try:
        # 读取待排产数据
        wait_df = pd.read_excel('Excellist2025.06.05/排产验证/et_wait_lot (test).xlsx')
        print("=" * 80)
        print("待排产批次字段详细分析")
        print("=" * 80)
        
        print("字段列表:")
        for i, col in enumerate(wait_df.columns, 1):
            print(f"{i:2d}. {col}")
        
        # 检查DEVICE字段
        if 'DEVICE' in wait_df.columns:
            print(f"\n=== DEVICE字段分析 ===")
            device_counts = wait_df['DEVICE'].value_counts()
            print(f"唯一设备数量: {len(device_counts)}")
            print("设备分布:")
            print(device_counts)
            
        # 检查是否有DEVICE_NAME字段
        if 'DEVICE_NAME' in wait_df.columns:
            print(f"\n=== DEVICE_NAME字段分析 ===")
            device_name_counts = wait_df['DEVICE_NAME'].value_counts()
            print(f"唯一设备名称数量: {len(device_name_counts)}")
            print("设备名称分布:")
            print(device_name_counts)
        else:
            print(f"\n❌ 待排产数据中没有DEVICE_NAME字段！")
        
        # 读取已排产数据
        done_df = pd.read_excel('Excellist2025.06.05/排产验证/lotprioritydone(test).xlsx')
        print("\n" + "=" * 80)
        print("已排产批次字段详细分析")
        print("=" * 80)
        
        print("字段列表:")
        for i, col in enumerate(done_df.columns, 1):
            print(f"{i:2d}. {col}")
        
        # 检查DEVICE字段
        if 'DEVICE' in done_df.columns:
            print(f"\n=== DEVICE字段分析 ===")
            device_counts = done_df['DEVICE'].value_counts()
            print(f"唯一设备数量: {len(device_counts)}")
            print("设备分布:")
            print(device_counts)
            
        # 检查是否有DEVICE_NAME字段
        if 'DEVICE_NAME' in done_df.columns:
            print(f"\n=== DEVICE_NAME字段分析 ===")
            device_name_counts = done_df['DEVICE_NAME'].value_counts()
            print(f"唯一设备名称数量: {len(device_name_counts)}")
            print("设备名称分布:")
            print(device_name_counts)
        else:
            print(f"\n❌ 已排产数据中没有DEVICE_NAME字段！")
            
        # 检查HANDLER_ID字段（可能是设备分配信息）
        if 'HANDLER_ID' in done_df.columns:
            print(f"\n=== HANDLER_ID字段分析（设备分配信息）===")
            handler_counts = done_df['HANDLER_ID'].value_counts()
            print(f"唯一处理器数量: {len(handler_counts)}")
            print("处理器分布:")
            print(handler_counts)
        
        return wait_df, done_df
        
    except Exception as e:
        print(f"分析出错: {e}")
        return None, None

def analyze_device_mapping(wait_df, done_df):
    """分析设备映射关系"""
    if wait_df is None or done_df is None:
        return
    
    print("\n" + "=" * 80)
    print("设备映射关系分析")
    print("=" * 80)
    
    # 合并数据
    wait_device = wait_df[['LOT_ID', 'DEVICE', 'CHIP_ID']].copy()
    done_device = done_df[['LOT_ID', 'DEVICE', 'HANDLER_ID']].copy()
    
    merged = pd.merge(wait_device, done_device, on='LOT_ID', suffixes=('_wait', '_done'))
    
    print(f"成功匹配的批次数量: {len(merged)}")
    
    # 检查DEVICE字段是否匹配
    device_match = merged['DEVICE_wait'] == merged['DEVICE_done']
    print(f"DEVICE字段匹配率: {device_match.mean():.2%}")
    
    if device_match.mean() < 1.0:
        print(f"\nDEVICE字段不匹配的案例:")
        device_mismatch = merged[~device_match]
        print(device_mismatch[['LOT_ID', 'DEVICE_wait', 'DEVICE_done', 'HANDLER_ID']].head(10))
    
    # 分析DEVICE与HANDLER_ID的关系
    print(f"\n=== DEVICE与HANDLER_ID的映射关系 ===")
    device_handler_mapping = merged.groupby(['DEVICE_wait', 'HANDLER_ID']).size().reset_index(name='count')
    print("DEVICE -> HANDLER_ID 映射:")
    print(device_handler_mapping.sort_values('count', ascending=False))
    
    # 检查一个DEVICE是否对应多个HANDLER_ID
    device_handler_unique = merged.groupby('DEVICE_wait')['HANDLER_ID'].nunique()
    multi_handler_devices = device_handler_unique[device_handler_unique > 1]
    
    if len(multi_handler_devices) > 0:
        print(f"\n⚠️  一个DEVICE对应多个HANDLER_ID的情况:")
        for device, handler_count in multi_handler_devices.items():
            handlers = merged[merged['DEVICE_wait'] == device]['HANDLER_ID'].unique()
            print(f"  {device}: {handler_count}个处理器 -> {list(handlers)}")
    
    # 分析优先级分配
    if 'PRIORITY' in done_df.columns:
        print(f"\n=== 优先级分配分析 ===")
        priority_by_device = done_df.groupby('DEVICE')['PRIORITY'].agg(['count', 'mean', 'min', 'max', 'std'])
        print("各设备的优先级统计:")
        print(priority_by_device.round(2))
        
        priority_by_handler = done_df.groupby('HANDLER_ID')['PRIORITY'].agg(['count', 'mean', 'min', 'max', 'std'])
        print("\n各处理器的优先级统计:")
        print(priority_by_handler.round(2))

def check_scheduling_logic():
    """检查排产逻辑的合理性"""
    try:
        done_df = pd.read_excel('Excellist2025.06.05/排产验证/lotprioritydone(test).xlsx')
        
        print("\n" + "=" * 80)
        print("排产逻辑合理性检查")
        print("=" * 80)
        
        # 检查优先级是否连续
        priorities = sorted(done_df['PRIORITY'].unique())
        print(f"优先级范围: {min(priorities)} - {max(priorities)}")
        print(f"优先级总数: {len(priorities)}")
        
        # 检查是否有优先级跳跃
        gaps = []
        for i in range(len(priorities) - 1):
            if priorities[i+1] - priorities[i] > 1:
                gaps.append((priorities[i], priorities[i+1]))
        
        if gaps:
            print(f"⚠️  优先级存在跳跃: {gaps}")
        else:
            print("✅ 优先级连续，无跳跃")
        
        # 检查同一设备的优先级分布
        print(f"\n=== 同一设备内的优先级分布 ===")
        for device in done_df['DEVICE'].unique()[:5]:  # 只显示前5个设备
            device_data = done_df[done_df['DEVICE'] == device]
            priorities = sorted(device_data['PRIORITY'].tolist())
            print(f"{device}: {len(priorities)}个批次, 优先级范围 {min(priorities)}-{max(priorities)}")
            if len(priorities) > 1:
                gaps = [priorities[i+1] - priorities[i] for i in range(len(priorities) - 1)]
                avg_gap = np.mean(gaps)
                print(f"  平均优先级间隔: {avg_gap:.2f}")
        
        # 检查RELEASE_TIME与优先级的关系
        if 'RELEASE_TIME' in done_df.columns:
            print(f"\n=== RELEASE_TIME与优先级关系分析 ===")
            done_df['RELEASE_TIME'] = pd.to_datetime(done_df['RELEASE_TIME'])
            
            # 按设备分析
            for device in done_df['DEVICE'].unique()[:3]:  # 只分析前3个设备
                device_data = done_df[done_df['DEVICE'] == device].copy()
                device_data = device_data.sort_values('PRIORITY')
                
                print(f"\n{device} 设备:")
                print(f"  批次数量: {len(device_data)}")
                print(f"  优先级范围: {device_data['PRIORITY'].min()} - {device_data['PRIORITY'].max()}")
                print(f"  发布时间范围: {device_data['RELEASE_TIME'].min()} - {device_data['RELEASE_TIME'].max()}")
                
                # 检查优先级与发布时间的相关性
                correlation = device_data['PRIORITY'].corr(device_data['RELEASE_TIME'].astype(np.int64))
                print(f"  优先级与发布时间相关性: {correlation:.3f}")
        
    except Exception as e:
        print(f"排产逻辑检查出错: {e}")

def main():
    """主函数"""
    print("开始详细分析设备匹配逻辑...")
    
    # 详细字段分析
    wait_df, done_df = detailed_field_analysis()
    
    # 设备映射关系分析
    analyze_device_mapping(wait_df, done_df)
    
    # 排产逻辑合理性检查
    check_scheduling_logic()
    
    print("\n" + "=" * 80)
    print("设备匹配分析完成！")
    print("=" * 80)

if __name__ == "__main__":
    main() 