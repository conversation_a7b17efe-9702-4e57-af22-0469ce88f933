{% extends "base.html" %}

{% block title %}AEC-FT ICP - 半自动排产数据收集
<style>
:root {
    --primary-color: #b72424;
    --secondary-color: #d73027;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.navbar-brand, .nav-link.active {
    color: var(--primary-color) !important;
}

.card-header {
    background-color: var(--primary-color);
    color: white;
}
</style>
{% endblock %}



{% block extra_css %}
<link href="{{ url_for('static', filename='vendor/fullcalendar/core/main.css') }}" rel="stylesheet" />
<link href="{{ url_for('static', filename='vendor/fullcalendar/daygrid/main.css') }}" rel="stylesheet" />
<link href="{{ url_for('static', filename='vendor/fullcalendar/timegrid/main.css') }}" rel="stylesheet" />

<style>
    .fc-event {
        cursor: pointer;
    }
    .resource-list {
        max-height: calc(100vh - 300px);
        overflow-y: auto;
    }
    .order-list {
        max-height: calc(100vh - 300px);
        overflow-y: auto;
    }
    .batch-upload {
        margin-bottom: 0.5rem;
        padding: 0.75rem;
        background-color: #f8f9fa;
        border-radius: 4px;
    }
    .priority-settings {
        margin-bottom: 0.5rem;
        padding: 0.75rem;
        background-color: #f8f9fa;
        border-radius: 4px;
    }
    /* 进度条容器样式 */
    .progress-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .progress-container {
        background: white;
        padding: 20px;
        border-radius: 8px;
        width: 300px;
        text-align: center;
    }

    .progress {
        height: 20px;
        margin: 15px 0;
        border-radius: 5px;
        overflow: hidden;
        position: relative;
    }

    .progress-bar {
        background-color: #b72424 !important;
        height: 100%;
        position: relative;
    }

    .progress-text {
        margin-top: 10px;
        font-weight: bold;
    }

    .progress-percent {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        line-height: 20px;
        color: white;
        text-align: center;
        font-weight: bold;
        text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
    }

    /* 修改表格字体大小和行高 */
    .table {
        font-size: 0.8rem;
        line-height: 1.2;
    }
    
    /* 调整单元格内边距 */
    .table td, 
    .table th {
        padding: 0.3rem 0.5rem;
        white-space: nowrap;
    }
    
    /* 固定表头 */
    .table-responsive {
        position: relative;
    }
    
    .table thead th {
        position: sticky;
        top: 0;
        z-index: 1;
        background-color: #f8f9fa;
    }
    
    /* 调整预览表格的最大高度 */
    .table-responsive {
        max-height: 300px;
        overflow-y: auto;
    }
    
    /* 压缩数据预览区域 */
    .data-preview-section {
        margin-top: 1rem;
        margin-bottom: 1rem;
    }
    
    /* 增大排产结果表格空间 */
    .schedule-result-table {
        max-height: 60vh;
        min-height: 400px;
    }
    
    /* 确保水平滚动时内容清晰可见 */
    .table-responsive::-webkit-scrollbar {
        height: 8px;
        width: 8px;
    }
    
    .table-responsive::-webkit-scrollbar-track {
        background: #f1f1f1;
    }
    
    .table-responsive::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 4px;
    }
    
    .table-responsive::-webkit-scrollbar-thumb:hover {
        background: #555;
    }

    /* 修改主要按钮颜色 */
    .btn-primary {
        background-color: #b72424;
        border-color: #b72424;
    }
    .btn-primary:hover {
        background-color: #b72424;
        border-color: #b72424;
    }
    .btn-primary:focus {
        background-color: #b72424;
        border-color: #b72424;
        box-shadow: 0 0 0 0.25rem rgba(245, 34, 45, 0.25);
    }

    /* 修改信息按钮颜色 */
    .btn-info {
        background-color: #b72424;
        border-color: #b72424;
        color: white;
    }
    .btn-info:hover {
        background-color: #b54442;
        border-color: #b54442;
        color: white;
    }

    /* 修改徽章颜色 */
    .badge.bg-primary {
        background-color: #b72424 !important;
    }

    /* 🚀 表格搜索和分页样式优化 */
    #tableSearchSection .card {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: 1px solid #dee2e6;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    #tableSearchSection .input-group-text {
        background: #fff;
        border: 1px solid #ced4da;
        color: #6c757d;
    }

    #tableSearchSection .form-control,
    #tableSearchSection .form-select {
        border: 1px solid #ced4da;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    #tableSearchSection .form-control:focus,
    #tableSearchSection .form-select:focus {
        border-color: #b72424;
        box-shadow: 0 0 0 0.2rem rgba(183, 36, 36, 0.25);
    }

    /* 分页控件样式 */
    .pagination .page-link {
        color: #b72424;
        border: 1px solid #dee2e6;
        padding: 0.5rem 0.75rem;
        margin: 0 2px;
        border-radius: 6px;
        transition: all 0.15s ease-in-out;
    }

    .pagination .page-link:hover {
        color: #fff;
        background-color: #b72424;
        border-color: #b72424;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .pagination .page-item.active .page-link {
        background-color: #b72424;
        border-color: #b72424;
        color: #fff;
        box-shadow: 0 2px 4px rgba(183, 36, 36, 0.3);
    }

    .pagination .page-item.disabled .page-link {
        color: #6c757d;
        background-color: #fff;
        border-color: #dee2e6;
    }

    /* 表格头部固定样式 */
    .table thead.sticky-top th {
        position: sticky;
        top: 0;
        z-index: 10;
        background: linear-gradient(135deg, #b72424 0%, #d73027 100%);
        color: white;
        border: none;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    /* 表格行悬停效果优化 */
    .table tbody tr {
        transition: all 0.2s ease;
    }

    .table tbody tr:hover {
        background-color: rgba(183, 36, 36, 0.08);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transform: translateY(-1px);
    }

    /* 记录统计样式 */
    .text-muted {
        font-size: 0.875rem;
        font-weight: 500;
    }

    /* 搜索框动画效果 */
    #searchInput {
        transition: all 0.3s ease;
    }

    #searchInput:focus {
        transform: scale(1.02);
        box-shadow: 0 4px 12px rgba(183, 36, 36, 0.15);
    }

    /* 表格响应式优化 */
    @media (max-width: 768px) {
        #tableSearchSection .col-md-3,
        #tableSearchSection .col-md-2 {
            margin-bottom: 0.5rem;
        }
        
        .pagination .page-link {
            padding: 0.375rem 0.5rem;
            font-size: 0.875rem;
        }
    }

    /* 加载状态样式 */
    .table-loading {
        position: relative;
        opacity: 0.6;
        pointer-events: none;
    }

    .table-loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 32px;
        height: 32px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #b72424;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        transform: translate(-50%, -50%);
        z-index: 20;
    }

    @keyframes spin {
        0% { transform: translate(-50%, -50%) rotate(0deg); }
        100% { transform: translate(-50%, -50%) rotate(360deg); }
    }

    /* 修改链接颜色 */
    a {
        color: #b72424;
    }
    a:hover {
        color: #b72424;
    }

    /* 修改表格中的选中和悬停状态 */
    .table tbody tr:hover {
        background-color: #fff1f0;
    }
    .table tbody tr.selected {
        background-color: #fff1f0;
    }

    /* 修改分页激活状态颜色 */
    .page-item.active .page-link {
        background-color: #b72424;
        border-color: #b72424;
    }
    .page-link {
        color: #b72424;
    }
    .page-link:hover {
        color: #b72424;
    }

    /* 功能卡片样式 */
    .function-card {
        border: 1px solid #e0e0e0;
        border-radius: 6px;
        padding: 10px;
        margin-bottom: 10px;
        background-color: #fff;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
    }
    
    /* 紧凑的管理中心样式 */
    .management-center {
        padding: 0.75rem;
        margin-bottom: 0.75rem;
    }
    
    .management-center h4 {
        font-size: 1.1rem;
        margin-bottom: 0.75rem;
        font-weight: 600;
    }
    
    .management-center h6 {
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
        font-weight: 600;
    }
    
    /* 进一步压缩空间的样式 */
    .container-fluid {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }
    
    .card {
        margin-bottom: 0.75rem;
    }
    
    .card-body {
        padding: 0.75rem;
    }
    
    /* 紧凑的表单标签 */
    .form-label.small {
        font-size: 0.8rem;
        margin-bottom: 0.25rem;
        font-weight: 500;
    }
    
    /* 紧凑的按钮组 */
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
        line-height: 1.2;
    }
    
    /* 优化手动排产区域 */
    .manual-scheduling-section {
        border-top: 1px solid #e9ecef;
        padding-top: 0.5rem;
        margin-top: 0.5rem;
    }
    
    /* 数据预览区域优化 */
    .data-preview-section .table {
        margin-bottom: 0.5rem;
    }
    
    /* 排产结果统计行优化 */
    .schedule-stats {
        padding: 0.5rem 0;
        margin-bottom: 0.75rem;
        background-color: #f8f9fa;
        border-radius: 0.375rem;
    }
    
    .schedule-stats h5 {
        font-size: 1rem;
        margin-bottom: 0.25rem;
    }
    
    .schedule-stats small {
        font-size: 0.75rem;
    }
    
    /* 定时任务区域样式 */
    .scheduled-task-section {
        border-top: 1px solid #e9ecef;
        padding-top: 0.5rem;
        margin-top: 0.5rem;
    }
    
    .task-status-active {
        color: #28a745 !important;
        font-weight: 600;
    }
    
    .task-status-inactive {
        color: #6c757d !important;
    }
    
    /* 定时任务模态框样式 */
    .time-input-group {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .time-input {
        width: 60px;
        text-align: center;
    }
    
    .task-preview {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 0.75rem;
        margin-top: 1rem;
    }

    .function-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }

    .function-card .card-icon {
        font-size: 2rem;
        margin-bottom: 10px;
        color: #b72424;
    }

    .function-card .card-title {
        font-weight: 600;
        margin-bottom: 10px;
    }

    .function-card .card-text {
        color: #666;
        margin-bottom: 15px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-2">
        <div class="col">
            <div class="card">
                <div class="card-body management-center">
                    <h4 class="mb-3">Excel数据导入管理中心</h4>
                    
                    <div class="row">
                        <!-- 批次上传 -->
                        <div class="col-md-5">
                            <div class="batch-upload">
                                <h6 class="mb-2">Excel文件上传</h6>
                                <div class="mb-2">
                                    <div class="input-group input-group-sm">
                                        <input type="text" class="form-control" id="importPath" 
                                               placeholder="请输入Excel文件所在路径">
                                        <input type="file" id="folderSelector" webkitdirectory directory style="display: none;">
                                        <button class="btn btn-outline-secondary btn-sm" type="button" onclick="savePath()">
                                            <i class="fas fa-save"></i> 保存路径
                                        </button>
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <button type="button" class="btn btn-primary btn-sm" onclick="importFromDirectory()">
                                        <i class="fas fa-file-import me-1"></i>导入Excel文件
                                    </button>
                                </div>
                                
                                <!-- 🔥 新增定时排产功能 -->
                                <div class="scheduled-task-section">
                                    <h6 class="mb-2">
                                        <i class="fas fa-clock me-1"></i>定时排产
                                    </h6>
                                    <div class="d-flex gap-1">
                                        <button type="button" class="btn btn-warning btn-sm flex-grow-1" onclick="openScheduledTaskModal()">
                                            <i class="fas fa-calendar-alt me-1"></i>设置定时任务
                                        </button>
                                        <button type="button" class="btn btn-outline-warning btn-sm" onclick="viewScheduledTasks()" id="viewTasksBtn">
                                            <i class="fas fa-list me-1"></i>查看任务
                                        </button>
                                    </div>
                                    <div class="mt-2">
                                        <small class="text-muted" id="taskStatusText">当前无定时任务</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 导出操作 -->
                        <div class="col-md-7">
                            <div class="priority-settings">
                                <h6 class="mb-2">数据操作</h6>
                                <div class="d-flex gap-1 mb-2">
                                    <button type="button" class="btn btn-info btn-sm flex-grow-1" onclick="previewSchedule()">
                                        <i class="fas fa-eye me-1"></i>预览数据
                                    </button>
                                    <button type="button" class="btn btn-secondary btn-sm flex-grow-1" onclick="exportSchedule()">
                                        <i class="fas fa-download me-1"></i>导出数据
                                    </button>
                                    <button type="button" class="btn btn-danger btn-sm flex-grow-1" onclick="deleteSelectedFile()">
                                        <i class="fas fa-trash me-1"></i>删除当前数据
                                    </button>
                                </div>
                                
                                <!-- 🔥 新增手动排产功能区域 -->
                                <div class="manual-scheduling-section">
                                    <h6 class="mb-2">
                                        <i class="fas fa-cogs me-1"></i>手动排产
                                    </h6>
                                    
                                    <!-- 排产策略选择 -->
                                    <div class="row mb-2">
                                        <div class="col-md-6">
                                            <label class="form-label small">排产策略</label>
                                            <select class="form-select form-select-sm" id="manualScheduleStrategy">
                                                <option value="intelligent" selected>🧠 智能综合策略 (推荐)</option>
                                                <option value="deadline">📅 交期优先策略</option>
                                                <option value="product">📦 产品优先策略</option>
                                                <option value="value">💰 产值优先策略</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label small">优化目标</label>
                                            <select class="form-select form-select-sm" id="manualOptimizationTarget">
                                                <option value="balanced" selected>⚖️ 均衡优化 (推荐)</option>
                                                <option value="makespan">⏱️ 最小化完工时间</option>
                                                <option value="efficiency">🚀 最大化效率</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <!-- 操作按钮 -->
                                    <div class="d-flex gap-1">
                                        <button type="button" class="btn btn-primary btn-sm flex-grow-1" onclick="executeManualScheduling()">
                                            <i class="fas fa-play me-1"></i>手动排产
                                        </button>
                                        
                                        <button type="button" class="btn btn-info btn-sm flex-grow-1" onclick="viewScheduleHistory()">
                                            <i class="fas fa-history me-1"></i>历史记录
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

    <!-- 数据预览区域 -->
    <div class="data-preview-section">
        <div class="d-flex justify-content-between align-items-center mb-2">
            <h6 class="mb-0 small">数据预览</h6>
            <div class="d-flex gap-1">
                <select class="form-select form-select-sm" id="fileSelector" style="width: 180px; font-size: 0.8rem;">
                    <option value="">选择数据</option>
                </select>
                <button class="btn btn-sm btn-primary" onclick="refreshTableData()" style="font-size: 0.8rem;">
                    <i class="fas fa-sync-alt"></i> 刷新
                </button>
            </div>
        </div>
        <div class="table-responsive" style="max-height: 250px;">
            <table class="table table-sm table-hover" id="previewTable" style="font-size: 0.8rem;">
                <thead id="previewTableHeader">
                    <!-- 表头将动态生成 -->
                </thead>
                <tbody id="previewTableBody">
                    <!-- 表体将动态生成 -->
                </tbody>
            </table>
        </div>
        <!-- 添加分页导航 -->
        <nav>
            <ul class="pagination pagination-sm justify-content-center mt-2" id="tablePagination"></ul>
        </nav>
    </div>

    <!-- 🔥 新增排产结果显示区域 -->
    <div class="mt-4" id="scheduleResultSection" style="display: none;">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-list-alt me-1"></i>排产结果
                        <span class="badge bg-primary ms-2" id="scheduleResultCount">0</span>
                    </h6>
                    <div class="d-flex gap-2">
                        <button class="btn btn-sm btn-success" onclick="exportScheduleResult()">
                            <i class="fas fa-file-excel me-1"></i>导出Excel
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="clearScheduleResult()">
                            <i class="fas fa-trash-alt me-1"></i>清除结果
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- 排产统计信息 -->
                <div class="row text-center schedule-stats">
                    <div class="col">
                        <h5 id="totalBatches">0</h5>
                        <small class="text-muted">总批次数</small>
                    </div>
                    <div class="col">
                        <h5 id="scheduledBatches" class="text-success">0</h5>
                        <small class="text-muted">已排批次</small>
                    </div>
                    <div class="col">
                        <h5 id="usedStrategy">智能综合</h5>
                        <small class="text-muted">排产策略</small>
                    </div>
                    <div class="col">
                        <h5 id="executionTime">0s</h5>
                        <small class="text-muted">执行时间</small>
                    </div>
                </div>

                <!-- 表格搜索栏 -->
                <div class="row mb-3" id="tableSearchSection" style="display: none;">
                    <div class="col-12">
                        <div class="card border-0 bg-light">
                            <div class="card-body py-2">
                                <div class="row g-2 align-items-center">
                                    <div class="col-md-3">
                                        <div class="input-group input-group-sm">
                                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                                            <input type="text" class="form-control" id="searchInput" placeholder="搜索所有字段...">
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <select class="form-select form-select-sm" id="searchField">
                                            <option value="all">全部字段</option>
                                            <option value="HANDLER_ID">分选机</option>
                                            <option value="LOT_ID">工单号</option>
                                            <option value="DEVICE">产品名称</option>
                                            <option value="STAGE">工序阶段</option>
                                            <option value="PKG_PN">封装类型</option>
                                            <option value="PO_ID">订单号</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <select class="form-select form-select-sm" id="pageSize">
                                            <option value="10">每页10条</option>
                                            <option value="20" selected>每页20条</option>
                                            <option value="50">每页50条</option>
                                            <option value="100">每页100条</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="d-flex gap-1">
                                            <button class="btn btn-sm btn-outline-secondary" onclick="clearSearch()">
                                                <i class="fas fa-times"></i> 清除
                                            </button>
                                            <button class="btn btn-sm btn-outline-info" onclick="exportFilteredData()">
                                                <i class="fas fa-file-excel"></i> 导出筛选
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <small class="text-muted">
                                            共 <span id="totalRecords">0</span> 条，显示 <span id="filteredRecords">0</span> 条
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 排产结果表格 -->
                <div class="table-responsive schedule-result-table">
                    <table class="table table-sm table-hover table-striped">
                        <thead class="table-light sticky-top">
                            <tr>
                                <th>优先级</th>
                                <th>分选机</th>
                                <th>内部工单号</th>
                                <th>批次类型</th>
                                <th>数量</th>
                                <th>产品名称</th>
                                <th>封装类型</th>
                                <th>芯片名称</th>
                                <th>工序阶段</th>
                                <th>匹配类型</th>
                                <th>综合评分</th>
                                <th>预计加工时间</th>
                                <th>改机时间</th>
                                <th>订单号</th>
                                <th>流程ID</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody id="scheduleResultTableBody">
                            <!-- JS动态生成 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页控件 -->
                <div class="row mt-3" id="paginationSection" style="display: none;">
                    <div class="col-12">
                        <nav aria-label="表格分页">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="text-muted small">
                                    显示第 <span id="pageStart">1</span> - <span id="pageEnd">20</span> 条，共 <span id="pageTotal">0</span> 条记录
            </div>
                                <ul class="pagination pagination-sm mb-0" id="paginationList">
                                    <!-- 分页按钮动态生成 -->
                                </ul>
                            </div>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 🔥 历史记录模态框 -->
    <div class="modal fade" id="scheduleHistoryModal" tabindex="-1" aria-labelledby="scheduleHistoryModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="scheduleHistoryModalLabel">
                        <i class="fas fa-history me-2"></i>排产历史记录
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" onclick="ensureModalClose()"></button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive" style="max-height: 600px;">
                        <table class="table table-sm table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>时间</th>
                                    <th>排产策略</th>
                                    <th>批次数量</th>
                                    <th>执行时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="historyTableBody">
                                <!-- 历史记录将动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" onclick="ensureModalClose()">关闭</button>
                    <button type="button" class="btn btn-danger" onclick="clearAllHistory()">
                        <i class="fas fa-trash me-1"></i>清空历史
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 🔥 定时任务设置模态框 -->
    <div class="modal fade" id="scheduledTaskModal" tabindex="-1" aria-labelledby="scheduledTaskModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="scheduledTaskModalLabel">
                        <i class="fas fa-clock me-2"></i>设置定时排产任务
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="scheduledTaskForm">
                        <!-- 任务基本信息 -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">任务名称</label>
                                <input type="text" class="form-control" id="taskName" placeholder="例如：每日自动排产" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">任务类型</label>
                                <select class="form-select" id="taskType" onchange="updateTaskTypeOptions()">
                                    <option value="once">一次性任务</option>
                                    <option value="daily">每日重复</option>
                                    <option value="weekly">每周重复</option>
                                    <option value="interval">间隔重复</option>
                                </select>
                            </div>
                        </div>

                        <!-- 执行时间设置 -->
                        <div class="row mb-3" id="timeSettings">
                            <div class="col-md-6">
                                <label class="form-label">执行日期</label>
                                <input type="date" class="form-control" id="taskDate" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">执行时间</label>
                                <div class="time-input-group">
                                    <input type="number" class="form-control time-input" id="taskHour" min="0" max="23" placeholder="时" required>
                                    <span>:</span>
                                    <input type="number" class="form-control time-input" id="taskMinute" min="0" max="59" placeholder="分" required>
                                </div>
                            </div>
                        </div>

                        <!-- 周重复设置 -->
                        <div class="row mb-3" id="weeklySettings" style="display: none;">
                            <div class="col-12">
                                <label class="form-label">选择星期</label>
                                <div class="d-flex gap-2 flex-wrap">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="1" id="monday">
                                        <label class="form-check-label" for="monday">周一</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="2" id="tuesday">
                                        <label class="form-check-label" for="tuesday">周二</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="3" id="wednesday">
                                        <label class="form-check-label" for="wednesday">周三</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="4" id="thursday">
                                        <label class="form-check-label" for="thursday">周四</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="5" id="friday">
                                        <label class="form-check-label" for="friday">周五</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="6" id="saturday">
                                        <label class="form-check-label" for="saturday">周六</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="0" id="sunday">
                                        <label class="form-check-label" for="sunday">周日</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 间隔重复设置 -->
                        <div class="row mb-3" id="intervalSettings" style="display: none;">
                            <div class="col-md-6">
                                <label class="form-label">间隔时间</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="intervalValue" min="1" placeholder="间隔数值">
                                    <select class="form-select" id="intervalUnit">
                                        <option value="minutes">分钟</option>
                                        <option value="hours">小时</option>
                                        <option value="days">天</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">结束时间</label>
                                <input type="datetime-local" class="form-control" id="intervalEndTime">
                            </div>
                        </div>

                        <!-- 排产参数设置 -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">排产策略</label>
                                <select class="form-select" id="taskStrategy">
                                    <option value="intelligent">🧠 智能综合策略</option>
                                    <option value="deadline">📅 交期优先策略</option>
                                    <option value="product">📦 产品优先策略</option>
                                    <option value="value">💰 产值优先策略</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">优化目标</label>
                                <select class="form-select" id="taskTarget">
                                    <option value="balanced">⚖️ 均衡优化</option>
                                    <option value="makespan">⏱️ 最小化完工时间</option>
                                    <option value="efficiency">🚀 最大化效率</option>
                                </select>
                            </div>
                        </div>

                        <!-- 高级选项 -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="autoImport" checked>
                                    <label class="form-check-label" for="autoImport">
                                        执行前自动导入最新数据
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="emailNotification">
                                    <label class="form-check-label" for="emailNotification">
                                        完成后发送邮件通知
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- 任务预览 -->
                        <div class="task-preview" id="taskPreview">
                            <h6><i class="fas fa-eye me-1"></i>任务预览</h6>
                            <div id="taskPreviewContent">
                                <small class="text-muted">请填写任务信息以预览执行计划</small>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveScheduledTask()">
                        <i class="fas fa-save me-1"></i>保存任务
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 🔥 定时任务列表模态框 -->
    <div class="modal fade" id="scheduledTaskListModal" tabindex="-1" aria-labelledby="scheduledTaskListModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="scheduledTaskListModalLabel">
                        <i class="fas fa-list me-2"></i>定时任务管理
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive" style="max-height: 600px;">
                        <table class="table table-sm table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>任务名称</th>
                                    <th>类型</th>
                                    <th>执行时间</th>
                                    <th>排产策略</th>
                                    <th>状态</th>
                                    <th>下次执行</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="scheduledTaskTableBody">
                                <!-- 任务列表将动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-success" onclick="openScheduledTaskModal()">
                        <i class="fas fa-plus me-1"></i>新建任务
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 进度条 HTML -->
    <div class="progress-overlay" id="progressOverlay">
        <div class="progress-container">
            <div id="progressText" class="mb-2">正在导入...</div>
            <div class="progress">
                <div class="progress-bar" id="progressBar" role="progressbar" style="width: 0%"></div>
                <div id="progressPercent" class="progress-percent">0%</div>
            </div>
        </div>
    </div>
{% endblock %}
{% block extra_js %}
<script src="{{ url_for('static', filename='vendor/xlsx/xlsx.full.min.js') }}"></script>
<script>
// 检查XLSX库是否正确加载
document.addEventListener('DOMContentLoaded', function() {
    if (typeof XLSX === 'undefined') {
        console.error('XLSX库加载失败');
        alert('Excel导出功能不可用：XLSX库未正确加载');
        return;
    }
    console.log('XLSX库加载成功，版本:', XLSX.version);
});

// 存储导入的数据
let importedData = {
    fileData: {},
    currentFile: null
};

// 从固定目录导入数据
function importFromDirectory() {
    let path = document.getElementById('importPath').value.trim();
    if (!path) {
        alert('请输入Excel文件所在路径');
        return;
    }
    
    // 重置错误计数器
    window.progressErrorCount = 0;
    
    // 显示进度条
    showProgress(true);
    updateProgress(0, '正在准备导入...');
    
    // 规范化路径格式
    path = path.replace(/\\/g, '/');
    
    // 声明进度轮询器变量
    let progressChecker;
    
    // 先清理旧的进度文件
    fetch('/api/production/clear-import-progress', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(() => {
        updateProgress(0, '正在导入Excel文件...');
        
        // 启动进度轮询
        progressChecker = startProgressPolling();
        
        return fetch('/api/production/import-from-directory', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ path })
        });
    })
    .then(response => {
        if (!response.ok) {
            // 尝试解析错误响应
            return response.json().then(errorData => {
                const errorMessage = `HTTP ${response.status}: ${errorData.error || errorData.message || '服务器内部错误'}`;
                throw new Error(errorMessage);
            }).catch(() => {
                throw new Error(`HTTP ${response.status}: 服务器响应错误，无法解析错误信息`);
            });
        }
        return response.json();
    })
    .then(result => {
        if (result.error) {
            throw new Error(result.error);
        }
        
        // 停止进度轮询
        clearInterval(progressChecker);
        
        // 更新进度到100%
        updateProgress(100, '导入完成');
        
        setTimeout(() => {
            showProgress(false);
            
            // 构建更专业的导入结果信息
            let message = '🎉 Excel文件导入完成\n\n';
            message += `📋 导入状态：${result.message}\n\n`;
            
            // 添加详细的表导入统计
            if (result.details) {
                message += `📊 详细信息：${result.details}\n\n`;
            }
            
            // 添加总体统计摘要
            const totalFiles = result.total_files || 0;
            const successFiles = result.processed_files ? result.processed_files.length : 0;
            const failedFiles = result.failed_count || 0;
            const totalRecords = result.total_records || 0;
            const processingTime = result.processing_time || 0;
            
            message += '📈 导入统计摘要：\n';
            message += `  • 总文件数：${totalFiles}\n`;
            message += `  • 成功导入：${successFiles} 个文件\n`;
            message += `  • 导入失败：${failedFiles} 个文件\n`;
            message += `  • 总记录数：${totalRecords} 条\n`;
            message += `  • 处理耗时：${processingTime} 秒\n\n`;
            
            // 添加处理的文件列表和每个文件导入的记录数
            if (result.processed_files && result.processed_files.length > 0) {
                message += "✅ 成功导入的文件详情：\n";
                const processedFiles = [];
                
                result.processed_files.forEach(file => {
                    message += `  📄 ${file.file} → ${file.table}表：${file.records}条记录\n`;
                    processedFiles.push({
                        name: file.file,
                        table: file.table,
                        records: file.records
                    });
                });
                
                message += "\n";
                
                // 更新文件选择器
                updateFileSelector(processedFiles);
            }
            
            // 添加失败文件信息
            if (result.failed_files && result.failed_files.length > 0) {
                message += "❌ 导入失败的文件详情：\n";
                result.failed_files.forEach(file => {
                    message += `  📄 ${file.file}\n`;
                    message += `     错误：${file.error}\n`;
                });
                message += "\n";
                
                // 添加失败文件的解决建议
                message += "💡 失败文件处理建议：\n";
                message += "  • 检查文件格式是否正确\n";
                message += "  • 确认文件未被其他程序占用\n";
                message += "  • 验证文件内容是否符合导入规范\n\n";
            }
            
            // 添加下一步操作建议
            if (successFiles > 0) {
                message += "🎯 下一步操作：\n";
                message += "  • 可以开始进行智能排产\n";
                message += "  • 检查导入的数据是否正确\n";
                message += "  • 设置排产参数和策略\n";
            }
            
            // 刷新表格数据（放在弹窗之前执行）
            loadImportedFiles();
            
            // 强制显示导入结果总结弹窗
            alert(message);
        }, 500);
    })
    .catch(error => {
        // 停止进度轮询
        clearInterval(progressChecker);
        
        showProgress(false);
        console.error('导入错误:', error);
        
        // 构建详细的错误信息
        let errorMessage = '📋 Excel文件导入失败\n\n';
        errorMessage += `❌ 错误详情：${error.message || '未知错误'}\n\n`;
        errorMessage += '🔍 可能的解决方案：\n';
        errorMessage += '1. 检查文件路径是否正确\n';
        errorMessage += '2. 确认对该目录有读取权限\n';
        errorMessage += '3. 验证目录中包含正确格式的Excel文件\n';
        errorMessage += '4. 检查Excel文件是否被其他程序占用\n';
        errorMessage += '5. 确认文件名不包含特殊字符\n';
        errorMessage += '6. 检查网络连接是否正常\n\n';
        errorMessage += '💡 提示：如果问题持续存在，请联系系统管理员或查看服务器日志。';
        
        alert(errorMessage);
    });
}

// 开始轮询进度
function startProgressPolling() {
    // 每500毫秒检查一次进度
    const intervalId = setInterval(() => {
        fetch('/api/production/import-progress')
            .then(response => response.json())
            .then(data => {
                // 重置错误计数器
                window.progressErrorCount = 0;
                
                // 构建当前文件进度信息
                const currentFileProgress = {
                    current_file: data.current_file,
                    files_processed: data.files_processed || 0,
                    total_files: data.total_files || 0
                };
                
                // 更新进度条，包括当前文件进度
                updateProgress(data.percent, data.message, currentFileProgress);
                
                // 如果有错误，显示详细错误消息
                if (data.error) {
                    showProgress(false);
                    
                    let errorMessage = '📋 Excel文件导入过程中出现错误\n\n';
                    errorMessage += `❌ 错误详情：${data.message || '未知错误'}\n\n`;
                    
                    if (data.current_file) {
                        errorMessage += `📄 当前处理文件：${data.current_file}\n`;
                    }
                    
                    if (data.files_processed && data.total_files) {
                        errorMessage += `📊 处理进度：${data.files_processed}/${data.total_files} 个文件\n\n`;
                    }
                    
                    errorMessage += '🔍 可能的解决方案：\n';
                    errorMessage += '  • 检查当前文件格式是否正确\n';
                    errorMessage += '  • 确认文件未被占用\n';
                    errorMessage += '  • 验证文件权限设置\n';
                    errorMessage += '  • 重新尝试导入操作\n';
                    
                    alert(errorMessage);
                    clearInterval(intervalId);
                }
                
                // 如果状态是completed，显示完成信息但继续轮询
                if (data.status === 'completed') {
                    updateProgress(100, '导入已完成');
                }
                // 如果状态是idle，停止轮询
                else if (data.status === 'idle') {
                    clearInterval(intervalId);
                    // 不更新进度显示，保持当前的导入结果显示
                    return;
                }
                // 如果进度是100%，停止轮询
                else if (data.percent >= 100) {
                    clearInterval(intervalId);
                }
            })
            .catch(error => {
                console.error('获取进度失败:', error);
                // 如果连续多次获取进度失败，停止轮询
                if (!window.progressErrorCount) {
                    window.progressErrorCount = 0;
                }
                window.progressErrorCount++;
                
                if (window.progressErrorCount >= 10) {
                    console.warn('进度获取连续失败，停止轮询');
                    clearInterval(intervalId);
                    showProgress(false);
                    
                    let progressErrorMessage = '⚠️ 无法获取导入进度信息\n\n';
                    progressErrorMessage += '📋 当前状态：导入可能仍在后台运行\n\n';
                    progressErrorMessage += '🔄 建议操作：\n';
                    progressErrorMessage += '  • 稍等片刻后刷新页面\n';
                    progressErrorMessage += '  • 检查网络连接状态\n';
                    progressErrorMessage += '  • 查看是否有新数据导入\n';
                    progressErrorMessage += '  • 如果长时间无响应，请联系技术支持\n\n';
                    progressErrorMessage += '💡 提示：即使进度显示失败，文件导入可能已成功完成。';
                    
                    alert(progressErrorMessage);
                }
            });
    }, 500);
    
    return intervalId;
}

// 使用详细信息更新文件列表
function updateFileListWithDetails(files) {
    // 由于不再显示文件列表，此函数保留但不执行任何操作
    console.log('已导入文件:', files);
}

// 保存路径
function savePath() {
    const path = document.getElementById('importPath').value.trim();
    if (!path) {
        alert('请先输入路径');
        return;
    }
    
    fetch('/api/production/save-import-path', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ path })
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert('路径保存成功');
        } else {
            throw new Error(result.error || '保存失败');
        }
    })
    .catch(error => {
        console.error('保存错误:', error);
        alert('保存失败：' + error.message);
    });
}

// 页面加载时自动加载保存的路径和已导入文件
document.addEventListener('DOMContentLoaded', function() {
    // 🧹 页面加载时清理所有残留通知
    clearAllNotifications();
    console.log('🚀 半自动排产页面已加载，通知系统已初始化');
    
    loadSavedPath();
    loadImportedFiles();
    
    // 添加分页点击事件委托
    document.getElementById('tablePagination').addEventListener('click', function(e) {
        e.preventDefault();
        
        // 检查点击的是否是分页链接
        const pageLink = e.target.closest('.page-link');
        if (!pageLink) return;
        
        // 获取页码和表名
        const page = pageLink.dataset.page;
        const tableName = pageLink.dataset.table;
        
        // 检查是否是禁用状态的链接
        if (pageLink.parentElement.classList.contains('disabled')) return;
        
        // 加载对应页的数据
        loadFileData(tableName, parseInt(page));
    });
    
    // 🛡️ 添加全局错误捕获，防止未处理的错误显示
    window.addEventListener('error', function(event) {
        console.error('🚨 页面错误捕获:', event.error);
        // 不显示错误给用户，避免混乱
        event.preventDefault();
    });
    
    // 🛡️ 添加未处理的Promise拒绝捕获
    window.addEventListener('unhandledrejection', function(event) {
        console.error('🚨 未处理的Promise拒绝:', event.reason);
        event.preventDefault();
    });
});

// 加载保存的路径
function loadSavedPath() {
    fetch('/api/production/get-import-path')
        .then(response => response.json())
        .then(result => {
            if (result.success && result.path) {
                document.getElementById('importPath').value = result.path;
            }
        })
        .catch(error => {
            console.error('加载保存的路径失败:', error);
        });
}

// 加载已导入的文件列表
function loadImportedFiles() {
    fetch('/api/production/imported-files')
        .then(response => response.json())
        .then(result => {
            if (result.success && result.files) {
                updateFileSelector(result.files);
            }
        })
        .catch(error => {
            console.error('加载已导入文件失败:', error);
        });
}

// 更新文件选择器
function updateFileSelector(files) {
    const fileSelector = document.getElementById('fileSelector');
    fileSelector.innerHTML = '<option value="">选择文件</option>' +
        files.map(file => 
            `<option value="${file.name}">${file.name}</option>`
        ).join('');
}

// 文件选择器变化时更新预览表格
document.getElementById('fileSelector').addEventListener('change', function(e) {
    const filename = e.target.value;
    if (!filename) {
        clearPreviewTable();
        return;
    }
    
    // 加载选中文件的数据
    loadFileData(filename);
});

// 加载文件数据
function loadFileData(filename, page = 1) {
    if (!filename) return;
    
    document.getElementById('previewTableBody').innerHTML = '<tr><td colspan="100%" class="text-center">加载中...</td></tr>';
    
    fetch(`/api/production/file-data/${filename}?page=${page}`)
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                // 更新表头
                const header = document.getElementById('previewTableHeader');
                header.innerHTML = `<tr>${result.columns.map(col => `<th>${col}</th>`).join('')}</tr>`;
                
                // 更新表体
                const body = document.getElementById('previewTableBody');
                body.innerHTML = result.data.map(row => `
                    <tr>${result.columns.map(col => `<td>${row[col] || ''}</td>`).join('')}</tr>
                `).join('');
                
                // 更新分页
                updateTablePagination(result.page, result.total_pages, filename);
            } else {
                throw new Error(result.error || '加载数据失败');
            }
        })
        .catch(error => {
            console.error('加载文件数据失败:', error);
            document.getElementById('previewTableBody').innerHTML = 
                '<tr><td colspan="100%" class="text-center text-danger">加载失败</td></tr>';
        });
}

// 更新预览表格
function updatePreviewTable(data, columns) {
    const thead = document.querySelector('#previewTable thead');
    const tbody = document.querySelector('#previewTable tbody');
    
    thead.innerHTML = `<tr>${columns.map(col => `<th>${col}</th>`).join('')}</tr>`;
    tbody.innerHTML = data.map(row => `
        <tr>${columns.map(col => `<td>${row[col] || ''}</td>`).join('')}</tr>
    `).join('');
}

// 清空预览表格
function clearPreviewTable() {
    document.querySelector('#previewTable thead').innerHTML = '';
    document.querySelector('#previewTable tbody').innerHTML = '';
}

// 更新分页
function updateTablePagination(currentPage, totalPages, tableName) {
    const pagination = document.getElementById('tablePagination');
    let html = '';
    
    if (totalPages > 1) {
        // 上一页
        html += `
            <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${currentPage - 1}" data-table="${tableName}">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
        `;
        
        // 页码
        let startPage = Math.max(1, currentPage - 2);
        let endPage = Math.min(totalPages, currentPage + 2);
        
        if (startPage > 1) {
            html += `
                <li class="page-item">
                    <a class="page-link" href="#" data-page="1" data-table="${tableName}">1</a>
                </li>
            `;
            if (startPage > 2) {
                html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }
        
        for (let i = startPage; i <= endPage; i++) {
            html += `
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" data-page="${i}" data-table="${tableName}">${i}</a>
                </li>
            `;
        }
        
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
            html += `
                <li class="page-item">
                    <a class="page-link" href="#" data-page="${totalPages}" data-table="${tableName}">${totalPages}</a>
                </li>
            `;
        }
        
        // 下一页
        html += `
            <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${currentPage + 1}" data-table="${tableName}">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        `;
    }
    
    pagination.innerHTML = html;
}

// 刷新表格数据
async function refreshTableData() {
    try {
        // 先记住当前选中的文件
        const currentFile = document.getElementById('fileSelector').value;
        
        // 刷新文件列表
        await loadImportedFiles();
        
        // 如果有选中的文件，刷新表格数据
        if (currentFile) {
            loadFileData(currentFile);
        }
    } catch (error) {
        console.error('刷新失败:', error);
        alert('刷新失败，请重试');
    }
}

// 显示进度条
function showProgress(show = true) {
    const overlay = document.getElementById('progressOverlay');
    overlay.style.display = show ? 'flex' : 'none';
}

// 更新进度条
function updateProgress(percent, text, currentFileProgress) {
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    const percentValue = Math.round(percent);
    
    progressBar.style.width = `${percent}%`;
    
    // 更新百分比显示
    progressText.textContent = text;
    
    // 更新进度条中的百分比文本
    let percentText = document.getElementById('progressPercent');
    if (!percentText) {
        percentText = document.createElement('div');
        percentText.id = 'progressPercent';
        percentText.className = 'progress-percent';
        percentText.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
            z-index: 10;
        `;
        document.querySelector('.progress').style.position = 'relative';
        document.querySelector('.progress').appendChild(percentText);
    }
    percentText.textContent = `${percentValue}%`;
    
    // 如果有当前文件进度信息，显示更详细的信息
    if (currentFileProgress && currentFileProgress.current_file) {
        const detailText = `${text} (当前: ${currentFileProgress.current_file})`;
        progressText.textContent = detailText;
    }
}

// 预览数据
function previewSchedule() {
    const selectedFile = document.getElementById('fileSelector').value;
    if (!selectedFile) {
        alert('请先选择一个文件');
        return;
    }
    
    loadFileData(selectedFile);
}

// 导出数据
function exportSchedule() {
    const selectedFile = document.getElementById('fileSelector').value;
    if (!selectedFile) {
        alert('请先选择一个文件');
        return;
    }
    
    fetch(`/api/production/export-file/${selectedFile}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.blob();
        })
        .then(blob => {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = selectedFile;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            alert('导出成功');
        })
        .catch(error => {
            console.error('导出错误:', error);
            alert('导出失败：' + error.message);
        });
}

// 删除选中的文件
function deleteSelectedFile() {
    const selectedFile = document.getElementById('fileSelector').value;
    if (!selectedFile) {
        alert('请先选择一个文件');
        return;
    }
    
    if (!confirm(`确定要删除选中的文件 ${selectedFile} 吗？`)) {
        return;
    }
    
    fetch('/api/production/delete-file', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ file: selectedFile })
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert(`成功删除文件 ${selectedFile}`);
            refreshTableData();
        } else {
            throw new Error(result.error || '删除失败');
        }
    })
    .catch(error => {
        console.error('删除错误:', error);
        alert('删除失败：' + error.message);
    });
}

// ==================== 🔥 手动排产功能实现 ====================

// 全局变量存储排产结果
let currentScheduleResult = null;

// 执行手动排产（清理版本，移除冲突代码）
function executeManualScheduling() {
    const strategy = document.getElementById('manualScheduleStrategy').value;
    const target = document.getElementById('manualOptimizationTarget').value;
    
    // 清除所有可能的旧通知
    clearAllNotifications();
    
    // 显示进度条
    showProgress(true);
    updateProgress(0, '正在初始化排产参数...', null);
    
    const requestData = {
        algorithm: strategy,
        optimization_target: target,
        time_limit: 30,
        population_size: 100,
        auto_mode: false
    };
    
    console.log('🚀 开始手动排产，策略:', strategy, '目标:', target);
    
    // 进度模拟（更稳定的版本）
    let progress = 0;
    const progressInterval = setInterval(() => {
        progress += Math.random() * 15 + 5; // 5-20%递增
        if (progress > 85) progress = 85; // 不超过85%
        updateProgress(progress, '正在执行排产算法...', null);
    }, 300);
    
    fetch('/api/production/auto-schedule', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
    })
    .then(response => {
        console.log('📡 收到服务器响应，状态:', response.status);
        return response.json();
    })
    .then(result => {
        clearInterval(progressInterval);
        console.log('📊 排产结果:', result);
        
        if (result.success && result.schedule && result.schedule.length > 0) {
            updateProgress(100, '排产完成！', null);
            
            setTimeout(() => {
                showProgress(false);
                
                // 存储排产结果
                currentScheduleResult = result;
                
                // 显示排产结果
                displayScheduleResult(result);
                
                // 保存按钮已移除，将自动保存
                
                // 自动保存排产结果
                saveScheduleResult();
            }, 800);
        } else {
            clearInterval(progressInterval);
            showProgress(false);
            console.error('❌ 排产失败:', result);
            
            // 🔧 改进：显示详细的错误信息和建议
            let errorMessage = result.message || '没有生成有效的排产记录';
            if (result.data_sources) {
                const suggestions = result.data_sources.suggestions || [];
                if (suggestions.length > 0) {
                    errorMessage += '\n\n💡 建议：' + suggestions.join('; ');
                }
            }
            showNotification('❌ 排产失败', errorMessage, 'error');
        }
    })
    .catch(error => {
        clearInterval(progressInterval);
        showProgress(false);
        console.error('💥 排产异常:', error);
        showNotification('💥 排产异常', '网络错误或服务器异常: ' + error.message, 'error');
    });
}

// 全局变量存储表格数据和分页状态
let tableData = [];
let filteredData = [];
let currentPage = 1;
let pageSize = 20;

// 显示排产结果 
function displayScheduleResult(result) {
    const section = document.getElementById('scheduleResultSection');
    
    // 存储原始数据
    tableData = result.schedule || [];
    filteredData = [...tableData];
    
    // 更新统计信息
    document.getElementById('scheduleResultCount').textContent = tableData.length;
    document.getElementById('totalBatches').textContent = result.metrics?.total_batches || tableData.length;
    document.getElementById('scheduledBatches').textContent = tableData.length;
    
    const strategyNames = {
        'intelligent': '🧠 智能综合',
        'deadline': '📅 交期优先',
        'product': '📦 产品优先', 
        'value': '💰 产值优先'
    };
    document.getElementById('usedStrategy').textContent = strategyNames[result.metrics?.algorithm] || '未知';
    document.getElementById('executionTime').textContent = `${(result.execution_time || 0).toFixed(2)}s`;
    
    // 显示搜索和分页控件
    document.getElementById('tableSearchSection').style.display = 'block';
    document.getElementById('paginationSection').style.display = 'block';
    
    // 重置分页状态
    currentPage = 1;
    pageSize = parseInt(document.getElementById('pageSize').value);
    
    // 更新记录统计
    updateRecordStats();
    
    // 渲染表格
    renderTable();
    
    // 显示结果区域
    section.style.display = 'block';
    section.scrollIntoView({ behavior: 'smooth' });
    
    // 绑定搜索和分页事件
    bindTableEvents();
}

// 渲染表格数据
function renderTable() {
    const tbody = document.getElementById('scheduleResultTableBody');
    
    if (!tbody) {
        console.error('表格主体元素未找到');
        return;
    }
    
    // 计算分页数据
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const pageData = filteredData.slice(startIndex, endIndex);
    
    // 如果没有数据，显示提示信息
    if (pageData.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="16" class="text-center text-muted py-4">
                    <i class="fas fa-search me-2"></i>
                    ${filteredData.length === 0 && tableData.length > 0 ? 
                        '未找到匹配的记录，请尝试调整搜索条件' : 
                        '暂无数据'}
                </td>
            </tr>
        `;
        renderPagination();
        updatePaginationInfo();
        return;
    }
    
    let html = '';
    pageData.forEach((item, index) => {
        const getMatchTypeBadge = (matchType) => {
            switch(matchType) {
                case '完全匹配': return '<span class="badge bg-success">完全匹配</span>';
                case 'KIT匹配': return '<span class="badge bg-info">KIT匹配</span>';
                case '小改机': return '<span class="badge bg-warning">小改机</span>';
                case '大改机': return '<span class="badge bg-secondary">大改机</span>';
                case '空闲设备': return '<span class="badge bg-primary">空闲设备</span>';
                default: return `<span class="badge bg-light text-dark">${matchType || 'N/A'}</span>`;
            }
        };
        
        const getScoreColor = (score) => {
            if (score >= 80) return 'text-success fw-bold';
            if (score >= 60) return 'text-primary fw-bold';
            if (score >= 40) return 'text-warning fw-bold';
            return 'text-danger fw-bold';
        };
        
        const score = parseFloat(item.comprehensive_score || 0);
        const processingTime = parseFloat(item.processing_time || 0);
        const changeoverTime = parseFloat(item.changeover_time || 0);
        
        html += `
            <tr>
                <td><span class="badge bg-primary">${item.PRIORITY || startIndex + index + 1}</span></td>
                <td><strong>${item.HANDLER_ID || ''}</strong></td>
                <td>${item.LOT_ID || ''}</td>
                <td><span class="badge bg-secondary">${item.LOT_TYPE || ''}</span></td>
                <td><span class="badge bg-light text-dark">${(item.GOOD_QTY || 0).toLocaleString()}</span></td>
                <td>${item.DEVICE || ''}</td>
                <td>${item.PKG_PN || ''}</td>
                <td><small class="text-muted">${item.CHIP_ID || ''}</small></td>
                <td><span class="badge bg-info">${item.STAGE || ''}</span></td>
                <td>${getMatchTypeBadge(item.match_type)}</td>
                <td><span class="${getScoreColor(score)}">${score.toFixed(1)}</span></td>
                <td>${processingTime > 0 ? processingTime.toFixed(1) + 'h' : 'N/A'}</td>
                <td>${changeoverTime > 0 ? changeoverTime.toFixed(0) + 'min' : '0min'}</td>
                <td>${item.PO_ID || ''}</td>
                <td><small class="text-muted">${item.FLOW_ID || ''}</small></td>
                <td><span class="badge bg-success">${item.WIP_STATE || 'QUEUE'}</span></td>
            </tr>
        `;
    });
    
    tbody.innerHTML = html;
    
    // 更新分页控件
    renderPagination();
    
    // 更新分页信息
    updatePaginationInfo();
}

// 绑定表格事件
function bindTableEvents() {
    // 搜索输入事件
    const searchInput = document.getElementById('searchInput');
    const searchField = document.getElementById('searchField');
    const pageSizeSelect = document.getElementById('pageSize');
    
    // 检查元素是否存在
    if (!searchInput || !searchField || !pageSizeSelect) {
        console.warn('搜索控件未找到，跳过事件绑定');
        return;
    }
    
    // 移除之前的事件监听器（避免重复绑定）
    searchInput.removeEventListener('input', handleSearch);
    searchField.removeEventListener('change', performSearch);
    pageSizeSelect.removeEventListener('change', handlePageSizeChange);
    
    // 防抖搜索
    let searchTimeout;
    function handleSearch() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            performSearch();
        }, 300);
    }
    
    function handlePageSizeChange() {
        pageSize = parseInt(this.value);
        currentPage = 1; // 重置到第一页
        renderTable();
    }
    
    // 绑定新的事件监听器
    searchInput.addEventListener('input', handleSearch);
    searchField.addEventListener('change', performSearch);
    pageSizeSelect.addEventListener('change', handlePageSizeChange);
}

// 执行搜索
function performSearch() {
    const searchTerm = document.getElementById('searchInput').value.trim().toLowerCase();
    const searchField = document.getElementById('searchField').value;
    
    if (!searchTerm) {
        filteredData = [...tableData];
    } else {
        filteredData = tableData.filter(item => {
            if (searchField === 'all') {
                // 搜索所有字段
                return Object.values(item).some(value => 
                    String(value || '').toLowerCase().includes(searchTerm)
                );
            } else {
                // 搜索指定字段
                return String(item[searchField] || '').toLowerCase().includes(searchTerm);
            }
        });
    }
    
    // 重置到第一页
    currentPage = 1;
    
    // 更新统计
    updateRecordStats();
    
    // 重新渲染表格
    renderTable();
}

// 更新记录统计
function updateRecordStats() {
    document.getElementById('totalRecords').textContent = tableData.length;
    document.getElementById('filteredRecords').textContent = filteredData.length;
}

// 渲染分页控件
function renderPagination() {
    const totalPages = Math.ceil(filteredData.length / pageSize);
    const paginationList = document.getElementById('paginationList');
    
    if (totalPages <= 1) {
        paginationList.innerHTML = '';
        return;
    }
    
    let html = '';
    
    // 上一页
    html += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage - 1}); return false;">
                <i class="fas fa-chevron-left"></i>
            </a>
        </li>
    `;
    
    // 页码
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    // 第一页
    if (startPage > 1) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(1); return false;">1</a></li>`;
        if (startPage > 2) {
            html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
    }
    
    // 中间页码
    for (let i = startPage; i <= endPage; i++) {
        html += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="changePage(${i}); return false;">${i}</a>
            </li>
        `;
    }
    
    // 最后一页
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
        html += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${totalPages}); return false;">${totalPages}</a></li>`;
    }
    
    // 下一页
    html += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage + 1}); return false;">
                <i class="fas fa-chevron-right"></i>
            </a>
        </li>
    `;
    
    paginationList.innerHTML = html;
}

// 更新分页信息
function updatePaginationInfo() {
    const startRecord = (currentPage - 1) * pageSize + 1;
    const endRecord = Math.min(currentPage * pageSize, filteredData.length);
    
    document.getElementById('pageStart').textContent = filteredData.length > 0 ? startRecord : 0;
    document.getElementById('pageEnd').textContent = endRecord;
    document.getElementById('pageTotal').textContent = filteredData.length;
}

// 切换页面
function changePage(page) {
    const totalPages = Math.ceil(filteredData.length / pageSize);
    if (page < 1 || page > totalPages) return;
    
    currentPage = page;
    renderTable();
}

// 清除搜索
function clearSearch() {
    document.getElementById('searchInput').value = '';
    document.getElementById('searchField').value = 'all';
    performSearch();
}

// 导出筛选后的数据
function exportFilteredData() {
    if (!filteredData || filteredData.length === 0) {
        alert('没有可导出的数据');
        return;
    }
    
    // 准备导出数据
    const exportData = filteredData.map((item, index) => ({
        '序号': index + 1,
        '优先级': item.PRIORITY || '',
        '分选机': item.HANDLER_ID || '',
        '内部工单号': item.LOT_ID || '',
        '批次类型': item.LOT_TYPE || '',
        '数量': item.GOOD_QTY || 0,
        '产品名称': item.DEVICE || '',
        '封装类型': item.PKG_PN || '',
        '芯片名称': item.CHIP_ID || '',
        '工序阶段': item.STAGE || '',
        '匹配类型': item.match_type || '',
        '综合评分': item.comprehensive_score || 0,
        '预计加工时间': item.processing_time || 0,
        '改机时间': item.changeover_time || 0,
        '订单号': item.PO_ID || '',
        '流程ID': item.FLOW_ID || '',
        '状态': item.WIP_STATE || 'QUEUE'
    }));
    
    // 创建工作簿
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.json_to_sheet(exportData);
    
    // 设置列宽
    const colWidths = [
        {wch: 8}, {wch: 12}, {wch: 15}, {wch: 20}, {wch: 10}, {wch: 12},
        {wch: 15}, {wch: 12}, {wch: 12}, {wch: 12}, {wch: 12}, {wch: 12},
        {wch: 15}, {wch: 12}, {wch: 15}, {wch: 15}, {wch: 10}
    ];
    ws['!cols'] = colWidths;
    
    XLSX.utils.book_append_sheet(wb, ws, '筛选结果');
    
    // 导出文件
    const fileName = `排产结果筛选_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.xlsx`;
    XLSX.writeFile(wb, fileName);
    
    showNotification('导出成功', `已导出 ${filteredData.length} 条筛选记录`, 'success');
}

// 获取优先级徽章样式
function getPriorityBadgeClass(priority) {
    switch(priority) {
        case 'high': return 'bg-danger';
        case 'medium': return 'bg-warning';
        case 'low': return 'bg-secondary';
        default: return 'bg-info';
    }
}

// 保存排产结果
function saveScheduleResult() {
    if (!currentScheduleResult || !currentScheduleResult.schedule) {
        console.log('没有可保存的排产结果');
        return;
    }
    
    // 转换数据格式以匹配现有API - 包含所有ET_WAIT_LOT字段和排产计算结果字段
    const records = currentScheduleResult.schedule.map((item, index) => ({
        ORDER: index + 1,
        // ET_WAIT_LOT 基础字段
        HANDLER_ID: item.HANDLER_ID || '',
        LOT_ID: item.LOT_ID || '',
        LOT_TYPE: item.LOT_TYPE || '',
        GOOD_QTY: item.GOOD_QTY || '',
        PROD_ID: item.PROD_ID || '',
        DEVICE: item.DEVICE || '',
        CHIP_ID: item.CHIP_ID || '',
        PKG_PN: item.PKG_PN || '',
        PO_ID: item.PO_ID || '',
        STAGE: item.STAGE || '',
        WIP_STATE: item.WIP_STATE || '',
        PROC_STATE: item.PROC_STATE || '',
        HOLD_STATE: item.HOLD_STATE || '',
        FLOW_ID: item.FLOW_ID || '',
        FLOW_VER: item.FLOW_VER || '',
        RELEASE_TIME: item.RELEASE_TIME || '',
        FAC_ID: item.FAC_ID || '',
        CREATE_TIME: item.CREATE_TIME || '',
        // 排产计算结果字段
        PRIORITY: item.PRIORITY || index + 1,
        match_type: item.match_type || '',
        comprehensive_score: item.comprehensive_score || 0,
        processing_time: item.processing_time || 0,
        changeover_time: item.changeover_time || 0,
        algorithm_version: item.algorithm_version || '',
        priority_score: item.priority_score || 0,
        estimated_hours: item.estimated_hours || 0,
        equipment_status: item.equipment_status || ''
    }));
    
    // 显示保存状态
    console.log('正在保存排产结果到数据库...');
    
    fetch('/api/production/save-priority-done', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ records: records })
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            console.log(`✅ 保存成功：${records.length} 条排产记录已保存到数据库`);
            
            // 保存到历史记录
            saveToHistory(currentScheduleResult);
        } else {
            throw new Error(result.error || '保存失败');
        }
    })
    .catch(error => {
        console.error('保存错误:', error);
        showNotification('保存失败', error.message || '保存失败，请重试', 'error');
    });
}

// 保存到历史记录（同时保存到localStorage和数据库）
function saveToHistory(scheduleResult) {
    // 1. 保存到localStorage（为了向后兼容）
    const history = JSON.parse(localStorage.getItem('scheduleHistory') || '[]');
    
    const historyItem = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        strategy: scheduleResult.metrics?.algorithm || 'unknown',
        batchCount: scheduleResult.schedule.length,
        executionTime: scheduleResult.execution_time || 0,
        data: scheduleResult
    };
    
    history.unshift(historyItem); // 添加到开头
    
    // 只保留最近50条记录
    if (history.length > 50) {
        history.splice(50);
    }
    
    localStorage.setItem('scheduleHistory', JSON.stringify(history));
    
    // 2. 保存到数据库
    const dbHistoryData = {
        schedule_results: scheduleResult.schedule,
        parameters: {
            strategy: scheduleResult.metrics?.algorithm || 'intelligent',
            optimization_target: scheduleResult.optimization_target || 'efficiency',
            time_limit: scheduleResult.time_limit || 300,
            population_size: scheduleResult.population_size || 100
        },
        statistics: {
            total_lots: scheduleResult.schedule.length,
            execution_time: scheduleResult.execution_time || 0
        }
    };
    
    fetch('/api/production/save-schedule-history', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(dbHistoryData)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            console.log('✅ 历史记录已保存到数据库');
        } else {
            console.warn('⚠️ 数据库保存失败，仅保存到localStorage:', result.error);
        }
    })
    .catch(error => {
        console.error('❌ 数据库保存错误:', error);
        console.log('📝 已保存到localStorage作为备份');
    });
}

// 查看历史记录
function viewScheduleHistory() {
    const history = JSON.parse(localStorage.getItem('scheduleHistory') || '[]');
    const tbody = document.getElementById('historyTableBody');
    
    if (history.length === 0) {
        tbody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">暂无历史记录</td></tr>';
    } else {
        const strategyNames = {
            'deadline': '交期优先',
            'product': '产品优先',
            'value': '产值优先',
            'intelligent': '智能综合'
        };
        
        let html = '';
        history.forEach(item => {
            const date = new Date(item.timestamp);
            html += `
                <tr>
                    <td>${date.toLocaleString()}</td>
                    <td>${strategyNames[item.strategy] || item.strategy}</td>
                    <td>${item.batchCount}</td>
                    <td>${item.executionTime.toFixed(2)}s</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="loadHistoryItem(${item.id})">
                            <i class="fas fa-eye me-1"></i>查看
                        </button>
                        <button class="btn btn-sm btn-outline-success ms-1" onclick="exportHistoryItem(${item.id})">
                            <i class="fas fa-file-excel me-1"></i>导出
                        </button>
                        <button class="btn btn-sm btn-outline-danger ms-1" onclick="deleteHistoryItem(${item.id})">
                            <i class="fas fa-trash me-1"></i>删除
                        </button>
                    </td>
                </tr>
            `;
        });
        tbody.innerHTML = html;
    }
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('scheduleHistoryModal'));
    modal.show();
}

// 加载历史记录项
function loadHistoryItem(id) {
    const history = JSON.parse(localStorage.getItem('scheduleHistory') || '[]');
    const item = history.find(h => h.id === id);
    
    if (item) {
        currentScheduleResult = item.data;
        displayScheduleResult(item.data);
        
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('scheduleHistoryModal'));
        modal.hide();
        
        showNotification('历史记录已加载', '可以重新保存或导出此结果', 'info');
    }
}

// 直接导出历史记录项（无需加载到当前结果）
function exportHistoryItem(id) {
    console.log('🔍 直接导出历史记录 ID:', id);
    
    const history = JSON.parse(localStorage.getItem('scheduleHistory') || '[]');
    const item = history.find(h => h.id === id);
    
    if (!item) {
        console.warn('⚠️ 未找到指定的历史记录');
        alert('未找到指定的历史记录！');
        return;
    }
    
    if (!item.data || !item.data.schedule) {
        console.warn('⚠️ 历史记录数据异常');
        alert('历史记录数据异常，无法导出！');
        return;
    }
    
    if (item.data.schedule.length === 0) {
        console.warn('⚠️ 历史记录为空');
        alert('该历史记录没有排产数据，无法导出！');
        return;
    }
    
    console.log(`✅ 准备导出历史记录: ${item.data.schedule.length} 条记录`);
    
    try {
        // 准备导出数据
        const exportData = item.data.schedule.map((record, index) => ({
            'ORDER': index + 1,
            'HANDLER_ID': record.HANDLER_ID || '',
            'LOT_ID': record.LOT_ID || '',
            'LOT_TYPE': record.LOT_TYPE || '',
            'GOOD_QTY': record.GOOD_QTY || '',
            'PROD_ID': record.PROD_ID || '',
            'DEVICE': record.DEVICE || '',
            'CHIP_ID': record.CHIP_ID || '',
            'PKG_PN': record.PKG_PN || '',
            'PO_ID': record.PO_ID || '',
            'STAGE': record.STAGE || '',
            'WIP_STATE': record.WIP_STATE || '',
            'PROC_STATE': record.PROC_STATE || '',
            'HOLD_STATE': record.HOLD_STATE || '',
            'FLOW_ID': record.FLOW_ID || '',
            'FLOW_VER': record.FLOW_VER || '',
            'RELEASE_TIME': record.RELEASE_TIME || '',
            'FAC_ID': record.FAC_ID || '',
            'CREATE_TIME': record.CREATE_TIME || ''
        }));
        
        // 使用XLSX库导出
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.json_to_sheet(exportData);
        
        // 设置列宽
        const colWidths = [
            { wch: 8 },  // ORDER
            { wch: 12 }, // HANDLER_ID
            { wch: 15 }, // LOT_ID
            { wch: 12 }, // LOT_TYPE
            { wch: 10 }, // GOOD_QTY
            { wch: 12 }, // PROD_ID
            { wch: 15 }, // DEVICE
            { wch: 15 }, // CHIP_ID
            { wch: 15 }, // PKG_PN
            { wch: 12 }, // PO_ID
            { wch: 10 }, // STAGE
            { wch: 12 }, // WIP_STATE
            { wch: 12 }, // PROC_STATE
            { wch: 12 }, // HOLD_STATE
            { wch: 12 }, // FLOW_ID
            { wch: 10 }, // FLOW_VER
            { wch: 18 }, // RELEASE_TIME
            { wch: 8 },  // FAC_ID
            { wch: 18 }  // CREATE_TIME
        ];
        ws['!cols'] = colWidths;
        
        XLSX.utils.book_append_sheet(wb, ws, "历史排产结果");
        
        // 生成文件名（包含历史记录的时间戳）
        const historyDate = new Date(item.timestamp);
        const dateStr = historyDate.toISOString().slice(0, 19).replace(/[:-]/g, '');
        const filename = `历史排产结果_${dateStr}.xlsx`;
        
        XLSX.writeFile(wb, filename);
        console.log(`✅ 历史记录导出成功: ${filename}`);
        showNotification('导出成功', `历史记录已导出为: ${filename}`, 'success');
        
    } catch (error) {
        console.error('💥 历史记录导出失败:', error);
        alert(`历史记录导出失败！\n\n错误详情: ${error.message}`);
    }
}

// 删除历史记录项
function deleteHistoryItem(id) {
    if (!confirm('确定要删除这条历史记录吗？')) {
        return;
    }
    
    try {
        const history = JSON.parse(localStorage.getItem('scheduleHistory') || '[]');
        const newHistory = history.filter(h => h.id !== id);
        localStorage.setItem('scheduleHistory', JSON.stringify(newHistory));
        
        // 更新表格内容，保持模态框打开状态
        updateHistoryTableDisplay(newHistory);
        
        showNotification('历史记录已删除', '', 'info');
        
    } catch (error) {
        console.error('删除历史记录失败:', error);
        showNotification('删除失败', '操作过程中出现错误', 'error');
    }
}

// 更新历史记录表格显示
function updateHistoryTableDisplay(history) {
    const tbody = document.getElementById('historyTableBody');
    
    if (!tbody) {
        console.error('找不到历史记录表格元素');
        return;
    }
    
    if (history.length === 0) {
        tbody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">暂无历史记录</td></tr>';
    } else {
        const strategyNames = {
            'deadline': '交期优先',
            'product': '产品优先', 
            'value': '产值优先',
            'intelligent': '智能综合'
        };
        
        let html = '';
        history.forEach(item => {
            const date = new Date(item.timestamp);
            html += `
                <tr>
                    <td>${date.toLocaleString()}</td>
                    <td>${strategyNames[item.strategy] || item.strategy}</td>
                    <td>${item.batchCount}</td>
                    <td>${item.executionTime.toFixed(2)}s</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="loadHistoryItem(${item.id})">
                            <i class="fas fa-eye me-1"></i>查看
                        </button>
                        <button class="btn btn-sm btn-outline-success ms-1" onclick="exportHistoryItem(${item.id})">
                            <i class="fas fa-file-excel me-1"></i>导出
                        </button>
                        <button class="btn btn-sm btn-outline-danger ms-1" onclick="deleteHistoryItem(${item.id})">
                            <i class="fas fa-trash me-1"></i>删除
                        </button>
                    </td>
                </tr>
            `;
        });
        tbody.innerHTML = html;
    }
}

// 确保模态框正确关闭
function ensureModalClose() {
    try {
        const modal = bootstrap.Modal.getInstance(document.getElementById('scheduleHistoryModal'));
        if (modal) {
            modal.hide();
        }
        
        // 移除所有可能的模态框背景
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => backdrop.remove());
        
        // 恢复body的overflow样式
        document.body.style.overflow = '';
        document.body.classList.remove('modal-open');
        
    } catch (error) {
        console.error('关闭模态框时出错:', error);
    }
}

// 清空所有历史记录
function clearAllHistory() {
    if (!confirm('确定要清空所有历史记录吗？此操作不可恢复！')) {
        return;
    }
    
    try {
        localStorage.removeItem('scheduleHistory');
        
        // 更新表格显示为空
        updateHistoryTableDisplay([]);
        
        showNotification('历史记录已清空', '', 'info');
        
    } catch (error) {
        console.error('清空历史记录失败:', error);
        showNotification('清空失败', '操作过程中出现错误', 'error');
    }
}

// 导出排产结果
function exportScheduleResult() {
    console.log('🔍 调试导出功能 - currentScheduleResult:', currentScheduleResult);
    
    if (!currentScheduleResult) {
        console.warn('⚠️ currentScheduleResult 为空，提示用户操作方式');
        alert('当前没有排产结果可以导出！\n\n📋 您可以通过以下方式导出：\n\n1️⃣ 导出当前排产结果：\n   • 点击"手动排产"按钮\n   • 等待排产完成\n   • 再点击"导出Excel"按钮\n\n2️⃣ 导出历史排产结果：\n   • 点击"历史记录"按钮\n   • 在历史记录中直接点击"导出"按钮');
        return;
    }
    
    if (!currentScheduleResult.schedule) {
        console.warn('⚠️ currentScheduleResult.schedule 为空');
        alert('排产结果数据异常，请重新执行排产！');
        return;
    }
    
    if (currentScheduleResult.schedule.length === 0) {
        console.warn('⚠️ 排产结果为空数组');
        alert('排产结果为空，没有可导出的数据！');
        return;
    }
    
    console.log(`✅ 准备导出 ${currentScheduleResult.schedule.length} 条排产记录`);
    
    try {
    
    // 准备导出数据
    const exportData = currentScheduleResult.schedule.map((item, index) => ({
        'ORDER': index + 1,
        'HANDLER_ID': item.HANDLER_ID || '',
        'LOT_ID': item.LOT_ID || '',
        'LOT_TYPE': item.LOT_TYPE || '',
        'GOOD_QTY': item.GOOD_QTY || '',
        'PROD_ID': item.PROD_ID || '',
        'DEVICE': item.DEVICE || '',
        'PKG_PN': item.PKG_PN || '',
        'CHIP_ID': item.CHIP_ID || '',
        'PO_ID': item.PO_ID || '',
        'STAGE': item.STAGE || '',
        'WIP_STATE': item.WIP_STATE || '',
        'PROC_STATE': item.PROC_STATE || '',
        'HOLD_STATE': item.HOLD_STATE || '',
        'FLOW_ID': item.FLOW_ID || '',
        'FLOW_VER': item.FLOW_VER || '',
        'RELEASE_TIME': item.RELEASE_TIME || '',
        'FAC_ID': item.FAC_ID || '',
        'CREATE_TIME': item.CREATE_TIME || '',
        // 新增字段
        '匹配类型': item.match_type || '',
        '综合评分': item.comprehensive_score || '',
        '预计加工时间': item.processing_time || '',
        '改机时间': item.changeover_time || ''
    }));
    
    // 使用XLSX库导出
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.json_to_sheet(exportData);
    
    // 设置列宽
    const colWidths = [
        { wch: 8 },  // ORDER
        { wch: 12 }, // HANDLER_ID
        { wch: 15 }, // LOT_ID
        { wch: 12 }, // LOT_TYPE
        { wch: 10 }, // GOOD_QTY
        { wch: 12 }, // PROD_ID
        { wch: 15 }, // DEVICE
        { wch: 15 }, // CHIP_ID
        { wch: 15 }, // PKG_PN
        { wch: 12 }, // PO_ID
        { wch: 10 }, // STAGE
        { wch: 12 }, // WIP_STATE
        { wch: 12 }, // PROC_STATE
        { wch: 12 }, // HOLD_STATE
        { wch: 12 }, // FLOW_ID
        { wch: 10 }, // FLOW_VER
        { wch: 18 }, // RELEASE_TIME
        { wch: 8 },  // FAC_ID
        { wch: 18 }, // CREATE_TIME
        { wch: 18 }, // 匹配类型
        { wch: 18 }, // 综合评分
        { wch: 18 }, // 预计加工时间
        { wch: 18 }  // 改机时间
    ];
    ws['!cols'] = colWidths;
    
    XLSX.utils.book_append_sheet(wb, ws, "排产结果");
    
    // 生成文件名
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
    const filename = `手动排产结果_${timestamp}.xlsx`;
    
        XLSX.writeFile(wb, filename);
        console.log(`✅ 排产结果导出成功: ${filename}`);
        showNotification('导出成功', `文件已保存为: ${filename}`, 'success');
        
    } catch (error) {
        console.error('💥 导出过程中出现错误:', error);
        alert(`导出失败！\n\n错误详情: ${error.message}\n\n请检查：\n1. 浏览器是否支持文件下载\n2. 是否有足够的存储空间\n3. 排产数据是否完整`);
    }
}

// 清除排产结果
function clearScheduleResult() {
    if (!confirm('确定要清除当前排产结果吗？')) {
        return;
    }
    
    currentScheduleResult = null;
    document.getElementById('scheduleResultSection').style.display = 'none';
    document.getElementById('scheduleResultTableBody').innerHTML = '';
    
    // 隐藏搜索和分页控件
    document.getElementById('tableSearchSection').style.display = 'none';
    document.getElementById('paginationSection').style.display = 'none';
    
    // 清除数据
    tableData = [];
    filteredData = [];
    currentPage = 1;
    
    // 重置搜索框
    if (document.getElementById('searchInput')) {
        document.getElementById('searchInput').value = '';
    }
    if (document.getElementById('searchField')) {
        document.getElementById('searchField').value = 'all';
    }
    
    showNotification('结果已清除', '排产结果和搜索状态已重置', 'info');
}

// 清除所有通知（防止冲突）
function clearAllNotifications() {
    // 移除所有position-fixed的alert通知
    const existingNotifications = document.querySelectorAll('.alert.position-fixed');
    existingNotifications.forEach(notification => {
        notification.remove();
    });
    
    // 移除可能的其他通知容器
    const otherNotifications = document.querySelectorAll('[class*="notification"], [class*="toast"], [class*="message"]');
    otherNotifications.forEach(notification => {
        if (notification.style.position === 'fixed' || notification.style.position === 'absolute') {
            notification.remove();
        }
    });
}

// 注释：已删除createJumpNotification和createSaveJumpNotification函数
// 这些函数之前用于页面跳转通知，现在已不需要

// 注释：已删除showScheduleCompletionOptions函数，排产完成后自动保存，无需用户选择

// 查看排产结果
function viewScheduleResults() {
    const section = document.getElementById('scheduleResultSection');
    section.scrollIntoView({ behavior: 'smooth' });
}

// 注释：已删除quickSaveAndView函数，现在统一使用saveScheduleResult函数

// 打开已排产界面 - 修改为不自动跳转
function openDoneLots() {
    showNotification('💡 提示', '请通过左侧菜单"排产管理 > 批次管理 > 已排产批次管理"查看排产结果', 'info');
}

// 关闭通知
function dismissNotification(button) {
    const notification = button.closest('.alert');
    if (notification) {
        notification.remove();
    }
}

// 显示通知消息（改进版本）
function showNotification(title, message, type = 'info') {
    // 先清除可能存在的同类型通知
    const existingSameType = document.querySelectorAll(`.alert.alert-${type === 'error' ? 'danger' : type}.position-fixed`);
    existingSameType.forEach(notification => notification.remove());
    
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 10000;
        min-width: 300px;
        max-width: 500px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    `;
    
    notification.innerHTML = `
        <strong>${title}</strong>
        ${message ? `<br><small>${message}</small>` : ''}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // 添加唯一标识
    notification.setAttribute('data-notification-id', Date.now());
    document.body.appendChild(notification);
    
    // 3秒后自动消失
    setTimeout(() => {
        if (notification && notification.parentNode) {
            notification.remove();
        }
    }, 3000);
    
    console.log(`📢 通知: ${title} - ${message}`);
}

// ==================== 🔥 定时任务功能实现 ====================

// 全局变量存储定时任务
let scheduledTasks = [];
let taskTimers = new Map(); // 存储定时器

// 页面加载时初始化定时任务
document.addEventListener('DOMContentLoaded', function() {
    loadScheduledTasks();
    updateTaskStatus();
    
    // 每分钟检查一次任务状态
    setInterval(updateTaskStatus, 60000);
    
    // 设置默认日期为今天
    const today = new Date().toISOString().split('T')[0];
    const taskDateInput = document.getElementById('taskDate');
    if (taskDateInput) {
        taskDateInput.value = today;
        taskDateInput.min = today; // 不允许选择过去的日期
    }
    
    // 监听表单变化以更新预览
    const form = document.getElementById('scheduledTaskForm');
    if (form) {
        form.addEventListener('input', updateTaskPreview);
        form.addEventListener('change', updateTaskPreview);
    }
});

// 打开定时任务设置模态框
function openScheduledTaskModal() {
    // 重置表单
    document.getElementById('scheduledTaskForm').reset();
    
    // 设置默认值
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('taskDate').value = today;
    document.getElementById('taskHour').value = '09';
    document.getElementById('taskMinute').value = '00';
    
    // 更新任务类型选项
    updateTaskTypeOptions();
    
    // 更新预览
    updateTaskPreview();
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('scheduledTaskModal'));
    modal.show();
}

// 更新任务类型选项
function updateTaskTypeOptions() {
    const taskType = document.getElementById('taskType').value;
    const timeSettings = document.getElementById('timeSettings');
    const weeklySettings = document.getElementById('weeklySettings');
    const intervalSettings = document.getElementById('intervalSettings');
    const taskDate = document.getElementById('taskDate');
    
    // 隐藏所有设置
    weeklySettings.style.display = 'none';
    intervalSettings.style.display = 'none';
    
    switch(taskType) {
        case 'once':
            timeSettings.style.display = 'block';
            taskDate.style.display = 'block';
            taskDate.required = true;
            break;
        case 'daily':
            timeSettings.style.display = 'block';
            taskDate.style.display = 'none';
            taskDate.required = false;
            break;
        case 'weekly':
            timeSettings.style.display = 'block';
            weeklySettings.style.display = 'block';
            taskDate.style.display = 'none';
            taskDate.required = false;
            break;
        case 'interval':
            timeSettings.style.display = 'none';
            intervalSettings.style.display = 'block';
            taskDate.style.display = 'none';
            taskDate.required = false;
            break;
    }
    
    updateTaskPreview();
}

// 更新任务预览
function updateTaskPreview() {
    const taskName = document.getElementById('taskName').value;
    const taskType = document.getElementById('taskType').value;
    const taskDate = document.getElementById('taskDate').value;
    const taskHour = document.getElementById('taskHour').value;
    const taskMinute = document.getElementById('taskMinute').value;
    const taskStrategy = document.getElementById('taskStrategy').value;
    const autoImport = document.getElementById('autoImport').checked;
    
    const previewContent = document.getElementById('taskPreviewContent');
    
    if (!taskName) {
        previewContent.innerHTML = '<small class="text-muted">请填写任务名称</small>';
        return;
    }
    
    let preview = `<strong>${taskName}</strong><br>`;
    
    // 任务类型和时间
    switch(taskType) {
        case 'once':
            if (taskDate && taskHour && taskMinute) {
                preview += `📅 一次性任务：${taskDate} ${String(taskHour).padStart(2, '0')}:${String(taskMinute).padStart(2, '0')}<br>`;
            }
            break;
        case 'daily':
            if (taskHour && taskMinute) {
                preview += `🔄 每日重复：每天 ${String(taskHour).padStart(2, '0')}:${String(taskMinute).padStart(2, '0')}<br>`;
            }
            break;
        case 'weekly':
            const selectedDays = [];
            ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'].forEach(day => {
                if (document.getElementById(day)?.checked) {
                    selectedDays.push(day);
                }
            });
            if (selectedDays.length > 0 && taskHour && taskMinute) {
                const dayNames = {
                    'sunday': '周日', 'monday': '周一', 'tuesday': '周二', 'wednesday': '周三',
                    'thursday': '周四', 'friday': '周五', 'saturday': '周六'
                };
                const dayStr = selectedDays.map(day => dayNames[day]).join('、');
                preview += `📅 每周重复：${dayStr} ${String(taskHour).padStart(2, '0')}:${String(taskMinute).padStart(2, '0')}<br>`;
            }
            break;
        case 'interval':
            const intervalValue = document.getElementById('intervalValue').value;
            const intervalUnit = document.getElementById('intervalUnit').value;
            if (intervalValue) {
                const unitNames = { 'minutes': '分钟', 'hours': '小时', 'days': '天' };
                preview += `⏰ 间隔重复：每 ${intervalValue} ${unitNames[intervalUnit]}<br>`;
            }
            break;
    }
    
    // 排产策略
    const strategyNames = {
        'intelligent': '🧠 智能综合策略',
        'deadline': '📅 交期优先策略',
        'product': '📦 产品优先策略',
        'value': '💰 产值优先策略'
    };
    preview += `🎯 排产策略：${strategyNames[taskStrategy]}<br>`;
    
    // 高级选项
    if (autoImport) {
        preview += `📥 自动导入最新数据<br>`;
    }
    
    previewContent.innerHTML = preview;
}

// 保存定时任务
function saveScheduledTask() {
    const form = document.getElementById('scheduledTaskForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    const taskData = {
        id: Date.now(),
        name: document.getElementById('taskName').value,
        type: document.getElementById('taskType').value,
        date: document.getElementById('taskDate').value,
        hour: parseInt(document.getElementById('taskHour').value),
        minute: parseInt(document.getElementById('taskMinute').value),
        strategy: document.getElementById('taskStrategy').value,
        target: document.getElementById('taskTarget').value,
        autoImport: document.getElementById('autoImport').checked,
        emailNotification: document.getElementById('emailNotification').checked,
        status: 'active',
        createdAt: new Date().toISOString(),
        lastExecuted: null,
        nextExecution: null
    };
    
    // 处理不同任务类型的特殊设置
    switch(taskData.type) {
        case 'weekly':
            taskData.weekdays = [];
            ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'].forEach(day => {
                if (document.getElementById(day)?.checked) {
                    taskData.weekdays.push(day);
                }
            });
            if (taskData.weekdays.length === 0) {
                alert('请至少选择一个星期');
                return;
            }
            break;
        case 'interval':
            taskData.intervalValue = parseInt(document.getElementById('intervalValue').value);
            taskData.intervalUnit = document.getElementById('intervalUnit').value;
            taskData.endTime = document.getElementById('intervalEndTime').value;
            if (!taskData.intervalValue) {
                alert('请设置间隔时间');
                return;
            }
            break;
    }
    
    // 计算下次执行时间
    taskData.nextExecution = calculateNextExecution(taskData);
    
    // 保存到数组
    scheduledTasks.push(taskData);
    
    // 保存到localStorage
    localStorage.setItem('scheduledTasks', JSON.stringify(scheduledTasks));
    
    // 设置定时器
    scheduleTask(taskData);
    
    // 更新状态显示
    updateTaskStatus();
    
    // 关闭模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('scheduledTaskModal'));
    modal.hide();
    
    showNotification('任务创建成功', `定时任务"${taskData.name}"已创建`, 'success');
}

// 计算下次执行时间
function calculateNextExecution(taskData) {
    const now = new Date();
    let nextExecution = new Date();
    
    switch(taskData.type) {
        case 'once':
            nextExecution = new Date(`${taskData.date}T${String(taskData.hour).padStart(2, '0')}:${String(taskData.minute).padStart(2, '0')}:00`);
            break;
        case 'daily':
            nextExecution.setHours(taskData.hour, taskData.minute, 0, 0);
            if (nextExecution <= now) {
                nextExecution.setDate(nextExecution.getDate() + 1);
            }
            break;
        case 'weekly':
            // 找到下一个符合条件的星期
            const currentDay = now.getDay();
            let daysUntilNext = 7;
            
            for (let day of taskData.weekdays) {
                const dayNum = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'].indexOf(day);
                let daysUntil = (dayNum - currentDay + 7) % 7;
                if (daysUntil === 0) {
                    // 今天，检查时间是否已过
                    const todayExecution = new Date();
                    todayExecution.setHours(taskData.hour, taskData.minute, 0, 0);
                    if (todayExecution > now) {
                        daysUntil = 0;
                    } else {
                        daysUntil = 7;
                    }
                }
                if (daysUntil < daysUntilNext) {
                    daysUntilNext = daysUntil;
                }
            }
            
            nextExecution.setDate(nextExecution.getDate() + daysUntilNext);
            nextExecution.setHours(taskData.hour, taskData.minute, 0, 0);
            break;
        case 'interval':
            // 间隔任务从现在开始计算
            const intervalMs = taskData.intervalValue * (
                taskData.intervalUnit === 'minutes' ? 60000 :
                taskData.intervalUnit === 'hours' ? 3600000 :
                86400000 // days
            );
            nextExecution = new Date(now.getTime() + intervalMs);
            break;
    }
    
    return nextExecution.toISOString();
}

// 设置定时器
function scheduleTask(taskData) {
    const nextExecution = new Date(taskData.nextExecution);
    const now = new Date();
    const delay = nextExecution.getTime() - now.getTime();
    
    if (delay > 0) {
        const timer = setTimeout(() => {
            executeScheduledTask(taskData);
        }, delay);
        
        taskTimers.set(taskData.id, timer);
        console.log(`⏰ 定时任务"${taskData.name}"已设置，将在 ${nextExecution.toLocaleString()} 执行`);
    }
}

// 执行定时任务
async function executeScheduledTask(taskData) {
    console.log(`🚀 开始执行定时任务: ${taskData.name}`);
    
    try {
        // 如果设置了自动导入，先导入数据
        if (taskData.autoImport) {
            // 这里可以调用导入函数，暂时跳过
            console.log('📥 自动导入数据（功能待实现）');
        }
        
        // 设置排产参数
        document.getElementById('manualScheduleStrategy').value = taskData.strategy;
        document.getElementById('manualOptimizationTarget').value = taskData.target;
        
        // 执行排产
        await executeManualScheduling();
        
        // 更新任务状态
        taskData.lastExecuted = new Date().toISOString();
        
        // 如果是重复任务，计算下次执行时间
        if (taskData.type !== 'once') {
            taskData.nextExecution = calculateNextExecution(taskData);
            scheduleTask(taskData); // 重新设置定时器
        } else {
            taskData.status = 'completed';
        }
        
        // 保存更新
        localStorage.setItem('scheduledTasks', JSON.stringify(scheduledTasks));
        updateTaskStatus();
        
        console.log(`✅ 定时任务"${taskData.name}"执行完成`);
        
        // 发送邮件通知（如果启用）
        if (taskData.emailNotification) {
            console.log('📧 发送邮件通知（功能待实现）');
        }
        
    } catch (error) {
        console.error(`❌ 定时任务"${taskData.name}"执行失败:`, error);
        taskData.status = 'error';
        localStorage.setItem('scheduledTasks', JSON.stringify(scheduledTasks));
        updateTaskStatus();
    }
}

// 加载已保存的定时任务
function loadScheduledTasks() {
    const saved = localStorage.getItem('scheduledTasks');
    if (saved) {
        scheduledTasks = JSON.parse(saved);
        
        // 重新设置所有活跃任务的定时器
        scheduledTasks.forEach(task => {
            if (task.status === 'active' && task.nextExecution) {
                const nextExecution = new Date(task.nextExecution);
                const now = new Date();
                
                if (nextExecution > now) {
                    scheduleTask(task);
                } else if (task.type !== 'once') {
                    // 重复任务需要重新计算下次执行时间
                    task.nextExecution = calculateNextExecution(task);
                    scheduleTask(task);
                } else {
                    // 一次性任务已过期
                    task.status = 'expired';
                }
            }
        });
        
        localStorage.setItem('scheduledTasks', JSON.stringify(scheduledTasks));
    }
}

// 更新任务状态显示
function updateTaskStatus() {
    const statusText = document.getElementById('taskStatusText');
    const activeTasks = scheduledTasks.filter(task => task.status === 'active');
    
    if (activeTasks.length === 0) {
        statusText.textContent = '当前无定时任务';
        statusText.className = 'text-muted';
    } else {
        const nextTask = activeTasks.reduce((earliest, task) => {
            return new Date(task.nextExecution) < new Date(earliest.nextExecution) ? task : earliest;
        });
        
        const nextTime = new Date(nextTask.nextExecution);
        const now = new Date();
        const timeDiff = nextTime.getTime() - now.getTime();
        
        if (timeDiff > 0) {
            const hours = Math.floor(timeDiff / (1000 * 60 * 60));
            const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
            
            statusText.innerHTML = `下次执行: ${nextTask.name}<br><small>${nextTime.toLocaleString()}</small>`;
            statusText.className = 'task-status-active';
        } else {
            statusText.textContent = '任务执行中...';
            statusText.className = 'task-status-active';
        }
    }
}

// 查看定时任务列表
function viewScheduledTasks() {
    const tbody = document.getElementById('scheduledTaskTableBody');
    
    if (scheduledTasks.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">暂无定时任务</td></tr>';
    } else {
        let html = '';
        scheduledTasks.forEach(task => {
            const typeNames = {
                'once': '一次性',
                'daily': '每日',
                'weekly': '每周',
                'interval': '间隔'
            };
            
            const strategyNames = {
                'intelligent': '智能综合',
                'deadline': '交期优先',
                'product': '产品优先',
                'value': '产值优先'
            };
            
            const statusBadges = {
                'active': '<span class="badge bg-success">活跃</span>',
                'paused': '<span class="badge bg-warning">暂停</span>',
                'completed': '<span class="badge bg-info">已完成</span>',
                'error': '<span class="badge bg-danger">错误</span>',
                'expired': '<span class="badge bg-secondary">已过期</span>'
            };
            
            let timeInfo = '';
            if (task.type === 'once') {
                timeInfo = `${task.date} ${String(task.hour).padStart(2, '0')}:${String(task.minute).padStart(2, '0')}`;
            } else if (task.type === 'daily') {
                timeInfo = `每天 ${String(task.hour).padStart(2, '0')}:${String(task.minute).padStart(2, '0')}`;
            } else if (task.type === 'weekly') {
                const dayNames = {
                    'sunday': '日', 'monday': '一', 'tuesday': '二', 'wednesday': '三',
                    'thursday': '四', 'friday': '五', 'saturday': '六'
                };
                const days = task.weekdays.map(day => dayNames[day]).join(',');
                timeInfo = `周${days} ${String(task.hour).padStart(2, '0')}:${String(task.minute).padStart(2, '0')}`;
            } else {
                const unitNames = { 'minutes': '分钟', 'hours': '小时', 'days': '天' };
                timeInfo = `每${task.intervalValue}${unitNames[task.intervalUnit]}`;
            }
            
            const nextExecution = task.nextExecution ? 
                new Date(task.nextExecution).toLocaleString() : 
                '-';
            
            html += `
                <tr>
                    <td>${task.name}</td>
                    <td>${typeNames[task.type]}</td>
                    <td>${timeInfo}</td>
                    <td>${strategyNames[task.strategy]}</td>
                    <td>${statusBadges[task.status]}</td>
                    <td><small>${nextExecution}</small></td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            ${task.status === 'active' ? 
                                `<button class="btn btn-outline-warning" onclick="pauseTask(${task.id})">暂停</button>` :
                                task.status === 'paused' ? 
                                `<button class="btn btn-outline-success" onclick="resumeTask(${task.id})">恢复</button>` :
                                ''
                            }
                            <button class="btn btn-outline-danger" onclick="deleteTask(${task.id})">删除</button>
                        </div>
                    </td>
                </tr>
            `;
        });
        tbody.innerHTML = html;
    }
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('scheduledTaskListModal'));
    modal.show();
}

// 暂停任务
function pauseTask(taskId) {
    const task = scheduledTasks.find(t => t.id === taskId);
    if (task) {
        task.status = 'paused';
        
        // 清除定时器
        if (taskTimers.has(taskId)) {
            clearTimeout(taskTimers.get(taskId));
            taskTimers.delete(taskId);
        }
        
        localStorage.setItem('scheduledTasks', JSON.stringify(scheduledTasks));
        updateTaskStatus();
        viewScheduledTasks(); // 刷新列表
        
        showNotification('任务已暂停', `任务"${task.name}"已暂停`, 'info');
    }
}

// 恢复任务
function resumeTask(taskId) {
    const task = scheduledTasks.find(t => t.id === taskId);
    if (task) {
        task.status = 'active';
        task.nextExecution = calculateNextExecution(task);
        scheduleTask(task);
        
        localStorage.setItem('scheduledTasks', JSON.stringify(scheduledTasks));
        updateTaskStatus();
        viewScheduledTasks(); // 刷新列表
        
        showNotification('任务已恢复', `任务"${task.name}"已恢复`, 'success');
    }
}

// 删除任务
function deleteTask(taskId) {
    const task = scheduledTasks.find(t => t.id === taskId);
    if (task && confirm(`确定要删除任务"${task.name}"吗？`)) {
        // 清除定时器
        if (taskTimers.has(taskId)) {
            clearTimeout(taskTimers.get(taskId));
            taskTimers.delete(taskId);
        }
        
        // 从数组中移除
        scheduledTasks = scheduledTasks.filter(t => t.id !== taskId);
        
        localStorage.setItem('scheduledTasks', JSON.stringify(scheduledTasks));
        updateTaskStatus();
        viewScheduledTasks(); // 刷新列表
        
        showNotification('任务已删除', `任务"${task.name}"已删除`, 'info');
    }
}


</script>
{% endblock %} 