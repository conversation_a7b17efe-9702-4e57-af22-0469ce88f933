#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的排产算法验证测试
"""

import pandas as pd
import sys
import os

def simple_validation_test():
    """简化的验证测试"""
    print("🚀 开始简化验证测试...")
    
    try:
        # 1. 加载验证数据
        print("📊 加载验证数据...")
        wait_df = pd.read_excel('Excellist2025.06.05/排产验证/et_wait_lot (test).xlsx')
        expected_df = pd.read_excel('Excellist2025.06.05/排产验证/lotprioritydone(test).xlsx')
        
        print(f"✅ 待排产数据: {len(wait_df)} 条记录")
        print(f"✅ 期望结果: {len(expected_df)} 条记录")
        
        # 2. 基本数据分析
        print("\n📈 基本数据分析:")
        print(f"待排产批次数: {wait_df['LOT_ID'].nunique()}")
        print(f"涉及设备数: {wait_df['DEVICE'].nunique()}")
        print(f"涉及阶段数: {wait_df['STAGE'].nunique()}")
        
        print(f"期望结果批次数: {expected_df['LOT_ID'].nunique()}")
        print(f"期望结果设备数: {expected_df['HANDLER_ID'].nunique()}")
        
        # 3. 数据完整性检查
        print("\n🔍 数据完整性检查:")
        
        # 检查LOT_ID匹配
        wait_lots = set(wait_df['LOT_ID'])
        expected_lots = set(expected_df['LOT_ID'])
        
        missing_in_expected = wait_lots - expected_lots
        extra_in_expected = expected_lots - wait_lots
        common_lots = wait_lots & expected_lots
        
        print(f"共同批次: {len(common_lots)}")
        print(f"期望结果中缺失: {len(missing_in_expected)}")
        print(f"期望结果中多余: {len(extra_in_expected)}")
        
        if len(missing_in_expected) > 0:
            print(f"缺失批次示例: {list(missing_in_expected)[:5]}")
        if len(extra_in_expected) > 0:
            print(f"多余批次示例: {list(extra_in_expected)[:5]}")
        
        # 4. 设备映射分析
        print("\n🔧 设备映射分析:")
        
        # 创建DEVICE+STAGE到HANDLER_ID的映射分析
        wait_device_stage = wait_df.groupby(['DEVICE', 'STAGE']).size().reset_index(name='count')
        expected_device_handler = expected_df.groupby(['DEVICE', 'STAGE'])['HANDLER_ID'].apply(lambda x: list(set(x))).reset_index()
        expected_device_handler['handler_count'] = expected_device_handler['HANDLER_ID'].apply(len)
        
        print(f"待排产DEVICE+STAGE组合数: {len(wait_device_stage)}")
        print(f"期望结果DEVICE+STAGE组合数: {len(expected_device_handler)}")
        
        # 一对多映射分析
        one_to_many = expected_device_handler[expected_device_handler['handler_count'] > 1]
        print(f"一对多映射组合数: {len(one_to_many)}")
        
        if len(one_to_many) > 0:
            print("一对多映射示例:")
            for _, row in one_to_many.head(3).iterrows():
                handlers = ', '.join(row['HANDLER_ID'])
                print(f"  {row['DEVICE']} + {row['STAGE']} -> {handlers}")
        
        # 5. 业务逻辑验证
        print("\n✅ 业务逻辑验证:")
        
        # 检查LOT_ID+STAGE的唯一性
        wait_lot_stage = wait_df.groupby(['LOT_ID', 'STAGE']).size()
        duplicates = wait_lot_stage[wait_lot_stage > 1]
        print(f"待排产数据LOT_ID+STAGE重复: {len(duplicates)} 个")
        
        expected_lot_stage = expected_df.groupby(['LOT_ID', 'STAGE']).size()
        expected_duplicates = expected_lot_stage[expected_lot_stage > 1]
        print(f"期望结果LOT_ID+STAGE重复: {len(expected_duplicates)} 个")
        
        # 检查HANDLER_ID的PRIORITY分配
        handler_priorities = expected_df.groupby('HANDLER_ID')['PRIORITY'].apply(list).reset_index()
        print(f"涉及的HANDLER_ID数量: {len(handler_priorities)}")
        
        # 检查PRIORITY序列的连续性
        discontinuous_handlers = []
        for _, row in handler_priorities.iterrows():
            priorities = sorted(row['PRIORITY'])
            expected_sequence = list(range(1, len(priorities) + 1))
            if priorities != expected_sequence:
                discontinuous_handlers.append(row['HANDLER_ID'])
        
        print(f"PRIORITY序列不连续的HANDLER: {len(discontinuous_handlers)}")
        if discontinuous_handlers:
            print(f"示例: {discontinuous_handlers[:3]}")
        
        # 6. 总结
        print("\n🎯 验证总结:")
        print(f"✅ 数据加载成功")
        print(f"✅ 数据规模匹配: {len(wait_df) == len(expected_df)}")
        print(f"✅ LOT_ID完整性: {len(missing_in_expected) == 0}")
        print(f"✅ 业务逻辑正确性: {len(expected_duplicates) == 0}")
        print(f"✅ PRIORITY序列正确性: {len(discontinuous_handlers) == 0}")
        
        overall_score = 0
        if len(wait_df) == len(expected_df):
            overall_score += 20
        if len(missing_in_expected) == 0:
            overall_score += 30
        if len(expected_duplicates) == 0:
            overall_score += 25
        if len(discontinuous_handlers) == 0:
            overall_score += 25
        
        print(f"\n🏆 验证得分: {overall_score}/100")
        
        if overall_score >= 90:
            print("🎉 验证结果: 优秀")
        elif overall_score >= 70:
            print("✅ 验证结果: 良好")
        else:
            print("⚠️ 验证结果: 需要改进")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    simple_validation_test()
